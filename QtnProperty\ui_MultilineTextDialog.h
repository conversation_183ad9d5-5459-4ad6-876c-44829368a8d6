/********************************************************************************
** Form generated from reading UI file 'MultilineTextDialog.ui'
**
** Created by: Qt User Interface Compiler version 5.9.9
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MULTILINETEXTDIALOG_H
#define UI_MULTILINETEXTDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QDialog>
#include <QtWidgets/QDialogButtonBox>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QPlainTextEdit>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_MultilineTextDialog
{
public:
    QVBoxLayout *verticalLayout;
    QPlainTextEdit *plainTextEdit;
    QDialogButtonBox *buttonBox;

    void setupUi(QDialog *MultilineTextDialog)
    {
        if (MultilineTextDialog->objectName().isEmpty())
            MultilineTextDialog->setObjectName(QStringLiteral("MultilineTextDialog"));
        MultilineTextDialog->resize(400, 300);
        MultilineTextDialog->setSizeGripEnabled(true);
        MultilineTextDialog->setModal(true);
        verticalLayout = new QVBoxLayout(MultilineTextDialog);
        verticalLayout->setObjectName(QStringLiteral("verticalLayout"));
        verticalLayout->setContentsMargins(6, 6, 6, 6);
        plainTextEdit = new QPlainTextEdit(MultilineTextDialog);
        plainTextEdit->setObjectName(QStringLiteral("plainTextEdit"));
        plainTextEdit->setTabChangesFocus(true);
        plainTextEdit->setLineWrapMode(QPlainTextEdit::WidgetWidth);

        verticalLayout->addWidget(plainTextEdit);

        buttonBox = new QDialogButtonBox(MultilineTextDialog);
        buttonBox->setObjectName(QStringLiteral("buttonBox"));
        buttonBox->setStandardButtons(QDialogButtonBox::Cancel|QDialogButtonBox::Ok);
        buttonBox->setCenterButtons(false);

        verticalLayout->addWidget(buttonBox);


        retranslateUi(MultilineTextDialog);

        QMetaObject::connectSlotsByName(MultilineTextDialog);
    } // setupUi

    void retranslateUi(QDialog *MultilineTextDialog)
    {
        MultilineTextDialog->setWindowTitle(QString());
    } // retranslateUi

};

namespace Ui {
    class MultilineTextDialog: public Ui_MultilineTextDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MULTILINETEXTDIALOG_H
