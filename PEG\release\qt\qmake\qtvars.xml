<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
<Qt_DEFINES_>_WINDOWS;WIN32;WIN64;QT_NO_DEBUG;QT_CORE_LIB;NDEBUG</Qt_DEFINES_>
<Qt_INCLUDEPATH_>D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include;D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtCore;D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\mkspecs\win32-msvc</Qt_INCLUDEPATH_>
<Qt_STDCPP_></Qt_STDCPP_>
<Qt_RUNTIME_>MultiThreadedDLL</Qt_RUNTIME_>
<Qt_CL_OPTIONS_>-Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding</Qt_CL_OPTIONS_>
<Qt_LIBS_>D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\lib\qtmain.lib;shell32.lib;D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\lib\Qt5Core.lib</Qt_LIBS_>
<Qt_LINK_OPTIONS_>"/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='6.0.0.0' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'"</Qt_LINK_OPTIONS_>
<QMake_QT_SYSROOT_></QMake_QT_SYSROOT_>
<QMake_QT_INSTALL_PREFIX_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64</QMake_QT_INSTALL_PREFIX_>
<QMake_QT_INSTALL_ARCHDATA_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64</QMake_QT_INSTALL_ARCHDATA_>
<QMake_QT_INSTALL_DATA_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64</QMake_QT_INSTALL_DATA_>
<QMake_QT_INSTALL_DOCS_>D:/Qt/Qt5.9.9/Docs/Qt-5.9.9</QMake_QT_INSTALL_DOCS_>
<QMake_QT_INSTALL_HEADERS_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/include</QMake_QT_INSTALL_HEADERS_>
<QMake_QT_INSTALL_LIBS_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/lib</QMake_QT_INSTALL_LIBS_>
<QMake_QT_INSTALL_LIBEXECS_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/bin</QMake_QT_INSTALL_LIBEXECS_>
<QMake_QT_INSTALL_BINS_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/bin</QMake_QT_INSTALL_BINS_>
<QMake_QT_INSTALL_TESTS_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/tests</QMake_QT_INSTALL_TESTS_>
<QMake_QT_INSTALL_PLUGINS_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/plugins</QMake_QT_INSTALL_PLUGINS_>
<QMake_QT_INSTALL_IMPORTS_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/imports</QMake_QT_INSTALL_IMPORTS_>
<QMake_QT_INSTALL_QML_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/qml</QMake_QT_INSTALL_QML_>
<QMake_QT_INSTALL_TRANSLATIONS_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/translations</QMake_QT_INSTALL_TRANSLATIONS_>
<QMake_QT_INSTALL_CONFIGURATION_></QMake_QT_INSTALL_CONFIGURATION_>
<QMake_QT_INSTALL_EXAMPLES_>D:/Qt/Qt5.9.9/Examples/Qt-5.9.9</QMake_QT_INSTALL_EXAMPLES_>
<QMake_QT_INSTALL_DEMOS_>D:/Qt/Qt5.9.9/Examples/Qt-5.9.9</QMake_QT_INSTALL_DEMOS_>
<QMake_QT_HOST_PREFIX_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64</QMake_QT_HOST_PREFIX_>
<QMake_QT_HOST_DATA_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64</QMake_QT_HOST_DATA_>
<QMake_QT_HOST_BINS_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/bin</QMake_QT_HOST_BINS_>
<QMake_QT_HOST_LIBS_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/lib</QMake_QT_HOST_LIBS_>
<QMake_QMAKE_SPEC_>win32-msvc</QMake_QMAKE_SPEC_>
<QMake_QMAKE_XSPEC_>win32-msvc</QMake_QMAKE_XSPEC_>
<QMake_QMAKE_VERSION_>3.1</QMake_QMAKE_VERSION_>
<QMake_QT_VERSION_>5.9.9</QMake_QT_VERSION_>
<QtBkup_QtHash>jZFBTsQwDEWvkhNMATFIs5iFx/4UQ5pEdoo0Qqj3vwVpVVSJDVl4E///7ed8qSBVrffr+XQ5XZbJP/np4fG8vDwHzoZw07SV4FUT/DoUy+/gOmjFtD1q1ZxGy3MZOHKeikY0FZqQ4Z7tUHnQxHEWFKpvvVEkso2guJtFrS2QTeHBq3ApvVGR0jjTCK+UhEyCzQ1+Qq9/l0e9Gdk9cFxyWem7z3Kw7MbQsv5xN+yP4TC24yG1T+MVf+317fAn5Xe8IYIc3z8=</QtBkup_QtHash>
    <QtVersion>5.9.9</QtVersion>
    <QtVersionMajor>5</QtVersionMajor>
    <QtVersionMinor>9</QtVersionMinor>
    <QtVersionPatch>9</QtVersionPatch>
  </PropertyGroup>
</Project>
