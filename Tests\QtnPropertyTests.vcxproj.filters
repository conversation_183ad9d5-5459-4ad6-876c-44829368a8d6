﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Generated Files">
      <UniqueIdentifier>{71ED8ED8-ACB9-4CE9-BBE1-E00B30144E11}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;moc;h;def;odl;idl;res;</Extensions>
    </Filter>
    <Filter Include="Generated Files">
      <UniqueIdentifier>{71ED8ED8-ACB9-4CE9-BBE1-E00B30144E11}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;moc;h;def;odl;idl;res;</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="PropertyEnum">
      <UniqueIdentifier>{E0D8C965-CC5F-43d7-AD63-FAEF0BBC0F85}-peg</UniqueIdentifier>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="PropertyEnum">
      <UniqueIdentifier>{E0D8C965-CC5F-43d7-AD63-FAEF0BBC0F85}-peg</UniqueIdentifier>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="my_moc_header">
      <UniqueIdentifier>{E0D8C965-CC5F-43d7-AD63-FAEF0BBC0F85}-peg_moc</UniqueIdentifier>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="my_moc_header">
      <UniqueIdentifier>{E0D8C965-CC5F-43d7-AD63-FAEF0BBC0F85}-peg_moc</UniqueIdentifier>
      <ParseFiles>false</ParseFiles>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="TestEnum.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="TestGeneratedProperty.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="TestProperty.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="TestEnum.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="TestGeneratedProperty.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="TestProperty.h">
      <Filter>Header Files</Filter>
    </QtMoc>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="moc_predefs.h.cbt">
      <Filter>Generated Files</Filter>
    </CustomBuild>
    <ClCompile Include="PEG\test.peg.cpp">
      <Filter>Generated Files</Filter>
    </ClCompile>
    <ClCompile Include="PEG\test2.peg.cpp">
      <Filter>Generated Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="PEG\test.pef">
      <Filter>PropertyEnum</Filter>
    </CustomBuild>
    <CustomBuild Include="PEG\test2.pef">
      <Filter>PropertyEnum</Filter>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="PEG\test.peg.h">
      <Filter>my_moc_header</Filter>
    </QtMoc>
    <QtMoc Include="PEG\test2.peg.h">
      <Filter>my_moc_header</Filter>
    </QtMoc>
  </ItemGroup>
</Project>