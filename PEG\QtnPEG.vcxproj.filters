﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Generated Files">
      <UniqueIdentifier>{71ED8ED8-ACB9-4CE9-BBE1-E00B30144E11}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;moc;h;def;odl;idl;res;</Extensions>
    </Filter>
    <Filter Include="Generated Files">
      <UniqueIdentifier>{71ED8ED8-ACB9-4CE9-BBE1-E00B30144E11}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;moc;h;def;odl;idl;res;</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Bison">
      <UniqueIdentifier>{E0D8C965-CC5F-43d7-AD63-FAEF0BBC0F85}-bison</UniqueIdentifier>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="Bison">
      <UniqueIdentifier>{E0D8C965-CC5F-43d7-AD63-FAEF0BBC0F85}-bison</UniqueIdentifier>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="Flex">
      <UniqueIdentifier>{E0D8C965-CC5F-43d7-AD63-FAEF0BBC0F85}-flex</UniqueIdentifier>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="Flex">
      <UniqueIdentifier>{E0D8C965-CC5F-43d7-AD63-FAEF0BBC0F85}-flex</UniqueIdentifier>
      <ParseFiles>false</ParseFiles>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="PropertyEnumGenerator.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="PropertyEnumGenerator.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <ClInclude Include="PropertyEnumGeneratorCommon.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="PropertyEnum.lexer.cpp">
      <Filter>Generated Files</Filter>
    </ClCompile>
    <ClCompile Include="PropertyEnum.parser.cpp">
      <Filter>Generated Files</Filter>
    </ClCompile>
    <CustomBuild Include="debug\moc_predefs.h.cbt">
      <Filter>Generated Files</Filter>
    </CustomBuild>
    <CustomBuild Include="release\moc_predefs.h.cbt">
      <Filter>Generated Files</Filter>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="PropertyEnum.y">
      <Filter>Bison</Filter>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="PropertyEnum.l">
      <Filter>Flex</Filter>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include=".\QtnPEG_resource.rc" />
  </ItemGroup>
</Project>