<FlowDocument xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" FontFamily="Segoe UI" FontSize="12">
  <FlowDocument.Tag><![CDATA[{
  "timestamp": "2025-08-07 15:43:32",
  "files": {
    "G:\\Code\\Qt\\QtnProperty\\PEG\\QtnPEG.vcxproj": {
      "before": "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",
      "after": "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"
    },
    "G:\\Code\\Qt\\QtnProperty\\PEG\\QtnPEG.vcxproj.filters": {
      "before": "5VfBbptAEM25UnvoFyDuC9gYGwfsyGBwc0tb9WYpwjAORLBLdyEiqvLvXWMnS4Lr0NSRmtaCA2t2Zt68mX3Dxw8nJ/ZZlaXSDVCWEDyRe4omS4BDEiX4aiKXxRqZ8tn0/Tv7gpJrCAuJv47ZRI6LIj9VVRbGkAVMyZKQEkbWhRKSTI3gBlKSA1UztiqTNFL7mqbL3Iwk2ecFZAtKyrx+5At+khZApXMcpmUEE3kBGGhQQCTxf4Btt0n8Z3/DyfcSziPARbJOgE5/jHre3OQXmrnOGA1cb4wcx+shT9McXesNBl6vd2errY2bSGqTXlUA3mBn0zDPrdAKq8rKSGjFVgRri0SplfCbArNstfHy1oCtboP/D6B8giDiLD1PyVgfjw3d1JA5duZosNIGyDQ9Bw37hu94Rt9zfKcrJbEVc1JiTkmcWQnmXODQqlj0AireevxfSUlD6JD/ge+O9JHfQ+5oZqCBPhqimTYcov5M789HRn/W9/2u+Rct0WyGOL+2VkFhBSzb3NUL2Pi30DgJI/jgOeVpc9MdDw3kuobPSYlGaDYf6sifeb7mOK7mm8YdWm3sHDqtLgLKoG7B6TpIGdhqY6XbkfSWYvVTqI6Q1jU388pZ/UsjtdXHatsWXzd1SZZznRX6y5WeS3dx6+Ey22kxoQo/ChpU7GSv2cZPtVB9MH2vjg8LwlcWJPiPTXdAWbKCZM5mFBG+9+OM2yib0tFGKUwLnLtZ5hlfPPMZwcpve0x35mt/HcAfpljh3QG/4PfJKNYGf29aQD9YTkq+OcBe0VkEq/Jqyee3y/3s7q3iY6CkkELA4PVdi3IT1SVQ5xS4UjMlVsJV0a7kZ5EK6w+MiiXhsIn2yC471PO+iJp0K7dt5LXstetXmOraTGKHSMcj52nb+UYdjuD7C7B6CGy12OJ06ZIIlp8LfuH7aJYX3qJ+9haX/Pul3qrQUJbUPVj5JLH9wJv+BA==",
      "after": "5VfRbpswFO3zpL7sCxDvBhJCQgpJFQhkeZjUbtpbpIqYm0IFNrNNlWrqv88haUhL1mRdK61bBA84+Nx77rHvMR9PT07c82WeKbfAeErJQG1phqoAwTROyfVALcUC2er58PSDe8HoDWChyNcJH6iJEMWZrnOcQB5xLU8xo5wuhIZprsdwCxktgOk5n5dpFuttwzBVCaMo7lRAPmG0LKpHORCmmQCmTAnOyhgG6gQIsEhArMh/gK+nKfLnfiPp9xKmMRCRLlJgwx+9VjC25YVGvtdHHT/oI88LWigwDM80Wp1O0Grdu3pj4iqTCjJYCiAr7nyIi8LBDl4unZxiJ3FiWDg0zpxU3gy44+o7L68BXH2d/H9A5RNEsVTpsCR9s9+3TNtAdt8bo87c6CDbDjzUbVuhF1jtwAu9YyVJnESKkkhJktxJidSCYGfJ4xdI8d7z/0pLhuGI+ndCv2f2whbyeyMLdcxeF42Mbhe1R2Z73LPao3YYHlv/ekvsboakuHHmkXAinq/u5QvU+LfYeCmn5Nk+FRhj2+93LeT7VihFiXtoNO6aKBwFoeF5vhHa1j2ar3Ce61YXEeNQbcHhIso4uPrOyHEt6T3lGmawfIWyLiTMG1f1L83U1R+7bdN8/cyneSF9tvZf6fTSusVdQMp848WUabIV7Eixsb3dbfzUC/Ut9IM7bgfqWHmUkj+GPszyUnymuI66n2HS5LdrGg1+FWjNbXN+ORBFVjunRPvdWH62ga/iHSa8p9S7pDW5I+AXmj45fjVob6Fr6s8uIa1YNa1XC1ZyQXNvdaisKx3DvLyeyVPbVcFAOhXXEg3PRVPPg9xq9C27eqgOyCCDiMNbhDxC230ZPVL3rsm8avtNLWuoYxdWPaMux6PgWTP4qju+QuwvwKtDUGO5Tc5mPo1hdinkRR6ymV0Ek+o5mFzJ83s1VWNYVfQ9XKWTrj9whj8B"
    }
  }
}]]></FlowDocument.Tag>
  <Section Margin="0,24" TextAlignment="Center">
    <Paragraph FontSize="24" FontWeight="Bold" Margin="12,0">
      <LineBreak />
      <Span Foreground="Gray">Qt Visual Studio Tools</Span>
    </Paragraph>
    <Paragraph FontSize="42" Margin="12,0" FontWeight="Bold">
      <Span TextDecorations="Underline">Project Format Conversion</Span>
    </Paragraph>
    <Paragraph Margin="12,8" FontSize="18">
      <Span>Report generated on 2025-08-07 15:43:32</Span>
    </Paragraph>
  </Section>
  <Section>
    <Paragraph FontSize="32" FontWeight="Bold" Margin="12,0">
      <Span>Files</Span>
    </Paragraph>
    <Paragraph Margin="24,12,0,0">
      <Span FontFamily="Consolas" FontSize="14" Background="WhiteSmoke"><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj]]></Span>
      <LineBreak />
      <Hyperlink NavigateUri="file://G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj?before">[Before]</Hyperlink>
      <Hyperlink NavigateUri="file://G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj?after">[After]</Hyperlink>
      <Hyperlink NavigateUri="file://G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj?diff&amp;before&amp;after">
                        [Diff before/after]
                    </Hyperlink>
      <Hyperlink NavigateUri="file://G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj?diff&amp;before&amp;current">
                        [Diff before/current]
                    </Hyperlink>
      <Hyperlink NavigateUri="file://G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj?diff&amp;after&amp;current">
                        [Diff after/current]
                    </Hyperlink>
      <LineBreak />
    </Paragraph>
    <Paragraph Margin="24,12,0,0">
      <Span FontFamily="Consolas" FontSize="14" Background="WhiteSmoke"><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj.filters]]></Span>
      <LineBreak />
      <Hyperlink NavigateUri="file://G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj.filters?before">[Before]</Hyperlink>
      <Hyperlink NavigateUri="file://G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj.filters?after">[After]</Hyperlink>
      <Hyperlink NavigateUri="file://G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj.filters?diff&amp;before&amp;after">
                        [Diff before/after]
                    </Hyperlink>
      <Hyperlink NavigateUri="file://G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj.filters?diff&amp;before&amp;current">
                        [Diff before/current]
                    </Hyperlink>
      <Hyperlink NavigateUri="file://G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj.filters?diff&amp;after&amp;current">
                        [Diff after/current]
                    </Hyperlink>
      <LineBreak />
    </Paragraph>
  </Section>
  <Section>
    <Paragraph FontSize="32" FontWeight="Bold" Margin="12,0">
      <Span>Changes</Span>
    </Paragraph>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Replacing paths with "$(QTDIR)"]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include]]></Span><Span><![CDATA[;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtCore]]></Span><Span><![CDATA[;release;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\mkspecs\win32-msvc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include]]></Span><Span><![CDATA[;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include\QtCore]]></Span><Span><![CDATA[;release;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\mkspecs\win32-msvc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalDependencies>D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\lib\qtmain]]></Span><Span><![CDATA[.lib;shell32.lib;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\lib\Qt5Core]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalDependencies>$(QTDIR)\lib\qtmain]]></Span><Span><![CDATA[.lib;shell32.lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt5Core]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalLibraryDirectories>D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\lib]]></Span><Span><![CDATA[;C:\openssl\lib;C:\Utils\my_sql\my_sql\lib;C:\Utils\postgresql\pgsql\lib;$(QTDIR)\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalLibraryDirectories>$(QTDIR)\lib]]></Span><Span><![CDATA[;C:\openssl\lib;C:\Utils\my_sql\my_sql\lib;C:\Utils\postgresql\pgsql\lib;$(QTDIR)\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include]]></Span><Span><![CDATA[;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtCore]]></Span><Span><![CDATA[;debug;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\mkspecs\win32-msvc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include]]></Span><Span><![CDATA[;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include\QtCore]]></Span><Span><![CDATA[;debug;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\mkspecs\win32-msvc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalDependencies>D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\lib\qtmaind]]></Span><Span><![CDATA[.lib;shell32.lib;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\lib\Qt5Cored]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalDependencies>$(QTDIR)\lib\qtmaind]]></Span><Span><![CDATA[.lib;shell32.lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt5Cored]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalLibraryDirectories>D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\lib]]></Span><Span><![CDATA[;C:\openssl\lib;C:\Utils\my_sql\my_sql\lib;C:\Utils\postgresql\pgsql\lib;$(QTDIR)\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalLibraryDirectories>$(QTDIR)\lib]]></Span><Span><![CDATA[;C:\openssl\lib;C:\Utils\my_sql\my_sql\lib;C:\Utils\postgresql\pgsql\lib;$(QTDIR)\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">PropertyEnumGenerator.h;release\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">PropertyEnumGenerator.h;release\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_CORE_LIB --compiler-flavor=msvc --include release/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.9.9/5.9.9/msvc2015_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/PEG ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.9.9/5.9.9/msvc2015_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.9.9/5.9.9/msvc2015_64/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" PropertyEnumGenerator.h -o release\moc_PropertyEnumGenerator.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_CORE_LIB --compiler-flavor=msvc --include release/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/PEG ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" PropertyEnumGenerator.h -o release\moc_PropertyEnumGenerator.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">PropertyEnumGenerator.h;debug\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">PropertyEnumGenerator.h;debug\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_CORE_LIB --compiler-flavor=msvc --include debug/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.9.9/5.9.9/msvc2015_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/PEG ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.9.9/5.9.9/msvc2015_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.9.9/5.9.9/msvc2015_64/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" PropertyEnumGenerator.h -o debug\moc_PropertyEnumGenerator.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_CORE_LIB --compiler-flavor=msvc --include debug/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/PEG ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" PropertyEnumGenerator.h -o debug\moc_PropertyEnumGenerator.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding /wd4065 /wd4267 /wd4005 -Zi -MDd -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E ]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp 2&gt;NUL &gt;debug\moc_predefs.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding /wd4065 /wd4267 /wd4005 -Zi -MDd -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E ]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp 2&gt;NUL &gt;debug\moc_predefs.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding /wd4065 /wd4267 /wd4005 -O2 -MD -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E ]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp 2&gt;NUL &gt;release\moc_predefs.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding /wd4065 /wd4267 /wd4005 -O2 -MD -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E ]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp 2&gt;NUL &gt;release\moc_predefs.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Replacing paths with "."]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_CORE_LIB --compiler-flavor=msvc --include release/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-IG:/Code/Qt/QtnProperty/PEG ]]></Span><Span><![CDATA[-I$(QTDIR)/include -I$(QTDIR)/include/QtCore -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" PropertyEnumGenerator.h -o release\moc_PropertyEnumGenerator.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_CORE_LIB --compiler-flavor=msvc --include release/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-I$(QTDIR)/include -I$(QTDIR)/include/QtCore -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" PropertyEnumGenerator.h -o release\moc_PropertyEnumGenerator.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_CORE_LIB --compiler-flavor=msvc --include debug/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-IG:/Code/Qt/QtnProperty/PEG ]]></Span><Span><![CDATA[-I$(QTDIR)/include -I$(QTDIR)/include/QtCore -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" PropertyEnumGenerator.h -o debug\moc_PropertyEnumGenerator.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_CORE_LIB --compiler-flavor=msvc --include debug/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-I$(QTDIR)/include -I$(QTDIR)/include/QtCore -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" PropertyEnumGenerator.h -o debug\moc_PropertyEnumGenerator.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    <ResourceCompile ]]></Span><Span Background="LightCoral"><![CDATA[Include="G:\Code\Qt\QtnProperty\PEG\QtnPEG_resource]]></Span><Span><![CDATA[.rc" />]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    <ResourceCompile ]]></Span><Span Background="LightGreen"><![CDATA[Include=".\QtnPEG_resource]]></Span><Span><![CDATA[.rc" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Converting custom build steps to Qt/MSBuild items]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalIncludeDirectories>.;]]></Span><Span><![CDATA[$(QTDIR)\include;$(QTDIR)\include\QtCore;release;$(QTDIR)\mkspecs\win32-msvc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;]]></Span><Span><![CDATA[$(QTDIR)\include;$(QTDIR)\include\QtCore;release;$(QTDIR)\mkspecs\win32-msvc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <OutputFile>$(Configuration)\moc_%(Filename).cpp</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <IncludePath>GeneratedFiles\$(ConfigurationName);GeneratedFiles;$(QTDIR)/mkspecs/win32-msvc;.;$(QTDIR)/include;$(QTDIR)/include/QtCore;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include;C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um</IncludePath>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <Define>UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;NDEBUG;QT_NO_DEBUG;QT_CORE_LIB</Define>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <CompilerFlavor>msvc</CompilerFlavor>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <Include>$(Configuration)/moc_predefs.h</Include>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <ExecutionDescription>Moc'ing %(Identity)...</ExecutionDescription>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <DynamicSource>output</DynamicSource>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    </QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalIncludeDirectories>.;]]></Span><Span><![CDATA[$(QTDIR)\include;$(QTDIR)\include\QtCore;debug;$(QTDIR)\mkspecs\win32-msvc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;]]></Span><Span><![CDATA[$(QTDIR)\include;$(QTDIR)\include\QtCore;debug;$(QTDIR)\mkspecs\win32-msvc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <OutputFile>$(Configuration)\moc_%(Filename).cpp</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <IncludePath>GeneratedFiles\$(ConfigurationName);GeneratedFiles;$(QTDIR)/mkspecs/win32-msvc;.;$(QTDIR)/include;$(QTDIR)/include/QtCore;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include;C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um</IncludePath>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <Define>UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;DEBUG;QT_CORE_LIB</Define>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <CompilerFlavor>msvc</CompilerFlavor>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <Include>$(Configuration)/moc_predefs.h</Include>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <ExecutionDescription>Moc'ing %(Identity)...</ExecutionDescription>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <DynamicSource>output</DynamicSource>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    </QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="PropertyEnumGenerator.]]></Span><Span Background="LightCoral"><![CDATA[h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="PropertyEnumGenerator.]]></Span><Span Background="LightGreen"><![CDATA[h" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">PropertyEnumGenerator.h;release\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_CORE_LIB --compiler-flavor=msvc --include release/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -I$(QTDIR)/include -I$(QTDIR)/include/QtCore -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" PropertyEnumGenerator.h -o release\moc_PropertyEnumGenerator.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MOC PropertyEnumGenerator.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">release\moc_PropertyEnumGenerator.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">PropertyEnumGenerator.h;debug\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_CORE_LIB --compiler-flavor=msvc --include debug/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -I$(QTDIR)/include -I$(QTDIR)/include/QtCore -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" PropertyEnumGenerator.h -o debug\moc_PropertyEnumGenerator.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MOC PropertyEnumGenerator.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">debug\moc_PropertyEnumGenerator.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="debug\moc_PropertyEnumGenerator.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="release\moc_PropertyEnumGenerator.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding /wd4065 /wd4267 /wd4005 -Zi -MDd -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E $(QTDIR)\mkspecs\features\data\dummy.cpp 2&gt;NUL &gt;]]></Span><Span Background="LightCoral"><![CDATA[debug\moc_predefs]]></Span><Span><![CDATA[.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding /wd4065 /wd4267 /wd4005 -Zi -MDd -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E $(QTDIR)\mkspecs\features\data\dummy.cpp 2&gt;NUL &gt;]]></Span><Span Background="LightGreen"><![CDATA[$(IntDir)\moc_predefs]]></Span><Span><![CDATA[.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">debug\moc_predefs]]></Span><Span><![CDATA[.h;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(IntDir)\moc_predefs]]></Span><Span><![CDATA[.h;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding /wd4065 /wd4267 /wd4005 -O2 -MD -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E $(QTDIR)\mkspecs\features\data\dummy.cpp 2&gt;NUL &gt;]]></Span><Span Background="LightCoral"><![CDATA[release\moc_predefs]]></Span><Span><![CDATA[.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding /wd4065 /wd4267 /wd4005 -O2 -MD -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E $(QTDIR)\mkspecs\features\data\dummy.cpp 2&gt;NUL &gt;]]></Span><Span Background="LightGreen"><![CDATA[$(IntDir)\moc_predefs]]></Span><Span><![CDATA[.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">release\moc_predefs]]></Span><Span><![CDATA[.h;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(IntDir)\moc_predefs]]></Span><Span><![CDATA[.h;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj.filters]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="PropertyEnumGenerator.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="PropertyEnumGenerator.h">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="debug\moc_PropertyEnumGenerator.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="release\moc_PropertyEnumGenerator.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Enabling multi-processor compilation]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <MultiProcessorCompilation>true</MultiProcessorCompilation>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <MultiProcessorCompilation>true</MultiProcessorCompilation>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Setting default Windows SDK]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Project format version]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<Keyword>Qt4VSv1.0</Keyword>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<Keyword>QtVS_v304</Keyword>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Fallback for QTMSBUILD environment variable]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtMsBuild Condition="'$(QtMsBuild)'=='' OR !Exists('$(QtMsBuild)\qt.targets')">$(MSBuildProjectDirectory)\QtMsBuild</QtMsBuild>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Default Qt properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <Import Project="$(QtMsBuild)\qt_defaults.props" Condition="Exists('$(QtMsBuild)\qt_defaults.props')" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Qt build settings]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Release|x64'" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Warn if Qt/MSBuild is not found]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <Target Name="QtMsBuildNotFound" BeforeTargets="CustomBuild;ClCompile" Condition="!Exists('$(QtMsBuild)\qt.targets') OR !Exists('$(QtMsBuild)\Qt.props')">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <Message Importance="High" Text="QtMsBuild: could not locate qt.targets, qt.props; project may not build correctly." />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  </Target>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Qt property sheet]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <Import Project="$(QtMsBuild)\Qt.props" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <Import Project="$(QtMsBuild)\Qt.props" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Qt targets]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <Import Project="$(QtMsBuild)\qt.targets" Condition="Exists('$(QtMsBuild)\qt.targets')" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Copying Qt build reference to QtInstall project property]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'" />]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[  ]]></Span><Span Background="LightCoral"><![CDATA[<PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" />]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtInstall>5.9.9_msvc2015_64</QtInstall>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  </PropertyGroup>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtInstall>5.9.9_msvc2015_64</QtInstall>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  </PropertyGroup>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing Qt module macros from compiler properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;NDEBUG;QT_NO_DEBUG;]]></Span><Span Background="LightCoral"><![CDATA[QT_CORE_LIB;]]></Span><Span><![CDATA[NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;NDEBUG;QT_NO_DEBUG;]]></Span><Span><![CDATA[NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;DEBUG;]]></Span><Span Background="LightCoral"><![CDATA[QT_CORE_LIB;]]></Span><Span><![CDATA[%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;DEBUG;]]></Span><Span><![CDATA[%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing Qt module include paths from compiler properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;]]></Span><Span Background="LightCoral"><![CDATA[$(QTDIR)\include;$(QTDIR)\include\QtCore;]]></Span><Span><![CDATA[release]]></Span><Span Background="LightCoral"><![CDATA[;$(QTDIR)\mkspecs\win32-msvc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;]]></Span><Span><![CDATA[release]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;]]></Span><Span Background="LightCoral"><![CDATA[$(QTDIR)\include;$(QTDIR)\include\QtCore;]]></Span><Span><![CDATA[debug]]></Span><Span Background="LightCoral"><![CDATA[;$(QTDIR)\mkspecs\win32-msvc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;]]></Span><Span><![CDATA[debug]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing Qt module libraries from linker properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalDependencies>$(QTDIR)\lib\qtmain.lib;shell32.lib;$(QTDIR)\lib\Qt5Core]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalDependencies>shell32]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalDependencies>$(QTDIR)\lib\qtmaind.lib;shell32.lib;$(QTDIR)\lib\Qt5Cored]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalDependencies>shell32]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing Qt lib path from linker properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalLibraryDirectories>$(QTDIR)\lib;C:\openssl\lib]]></Span><Span><![CDATA[;C:\Utils\my_sql\my_sql\lib;C:\Utils\postgresql\pgsql\lib]]></Span><Span Background="LightCoral"><![CDATA[;$(QTDIR)\lib]]></Span><Span><![CDATA[;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalLibraryDirectories>C:\openssl\lib]]></Span><Span><![CDATA[;C:\Utils\my_sql\my_sql\lib;C:\Utils\postgresql\pgsql\lib]]></Span><Span><![CDATA[;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalLibraryDirectories>$(QTDIR)\lib;C:\openssl\lib]]></Span><Span><![CDATA[;C:\Utils\my_sql\my_sql\lib;C:\Utils\postgresql\pgsql\lib]]></Span><Span Background="LightCoral"><![CDATA[;$(QTDIR)\lib]]></Span><Span><![CDATA[;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalLibraryDirectories>C:\openssl\lib]]></Span><Span><![CDATA[;C:\Utils\my_sql\my_sql\lib;C:\Utils\postgresql\pgsql\lib]]></Span><Span><![CDATA[;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing Qt module macros from resource compiler properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;NDEBUG;QT_NO_DEBUG;]]></Span><Span Background="LightCoral"><![CDATA[QT_CORE_LIB;]]></Span><Span><![CDATA[%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;NDEBUG;QT_NO_DEBUG;]]></Span><Span><![CDATA[%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;DEBUG;]]></Span><Span Background="LightCoral"><![CDATA[QT_CORE_LIB;]]></Span><Span><![CDATA[_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;DEBUG;]]></Span><Span><![CDATA[_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Adding Qt module names to QtModules project property]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtModules>core</QtModules>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtModules>core</QtModules>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Converting OutputFile to <tool>Dir and <tool>FileName]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtMocDir>$(Configuration)</QtMocDir>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtMocFileName>moc_%(Filename).cpp</QtMocFileName>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtMocDir>$(Configuration)</QtMocDir>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtMocFileName>moc_%(Filename).cpp</QtMocFileName>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing old properties from project items]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <OutputFile>$(Configuration)\moc_%(Filename).cpp</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <IncludePath>GeneratedFiles\$(ConfigurationName);GeneratedFiles;$(QTDIR)/mkspecs/win32-msvc;.;$(QTDIR)/include;$(QTDIR)/include/QtCore;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include;C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um</IncludePath>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Define>UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;NDEBUG;QT_NO_DEBUG;QT_CORE_LIB</Define>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <OutputFile>$(Configuration)\moc_%(Filename).cpp</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <IncludePath>GeneratedFiles\$(ConfigurationName);GeneratedFiles;$(QTDIR)/mkspecs/win32-msvc;.;$(QTDIR)/include;$(QTDIR)/include/QtCore;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include;C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um</IncludePath>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Define>UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;DEBUG;QT_CORE_LIB</Define>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
  </Section>
  <Section>
    <Paragraph />
  </Section>
</FlowDocument>
<!--CBo6wR4dCJsE8lPd0RY/X/BPj2tfeP4ofOeaOlRQsoY=-->
