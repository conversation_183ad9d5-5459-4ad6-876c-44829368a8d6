/****************************************************************************
** Meta object code from reading C++ file 'PropertyQSizeF.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../Core/PropertyQSizeF.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'PropertyQSizeF.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtnPropertyQSizeFBase_t {
    QByteArrayData data[1];
    char stringdata0[22];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQSizeFBase_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQSizeFBase_t qt_meta_stringdata_QtnPropertyQSizeFBase = {
    {
QT_MOC_LITERAL(0, 0, 21) // "QtnPropertyQSizeFBase"

    },
    "QtnPropertyQSizeFBase"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQSizeFBase[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyQSizeFBase::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyQSizeFBase::staticMetaObject = {
    { &QtnStructPropertyBase<QSizeF,QtnPropertyDoubleCallback>::staticMetaObject, qt_meta_stringdata_QtnPropertyQSizeFBase.data,
      qt_meta_data_QtnPropertyQSizeFBase,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQSizeFBase::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQSizeFBase::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQSizeFBase.stringdata0))
        return static_cast<void*>(this);
    return QtnStructPropertyBase<QSizeF,QtnPropertyDoubleCallback>::qt_metacast(_clname);
}

int QtnPropertyQSizeFBase::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnStructPropertyBase<QSizeF,QtnPropertyDoubleCallback>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyQSizeFCallback_t {
    QByteArrayData data[3];
    char stringdata0[34];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQSizeFCallback_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQSizeFCallback_t qt_meta_stringdata_QtnPropertyQSizeFCallback = {
    {
QT_MOC_LITERAL(0, 0, 25), // "QtnPropertyQSizeFCallback"
QT_MOC_LITERAL(1, 26, 0), // ""
QT_MOC_LITERAL(2, 27, 6) // "parent"

    },
    "QtnPropertyQSizeFCallback\0\0parent"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQSizeFCallback[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       2,   18, // constructors
       0,       // flags
       0,       // signalCount

 // constructors: parameters
    0x80000000 | 1, QMetaType::QObjectStar,    2,
    0x80000000 | 1,

 // constructors: name, argc, parameters, tag, flags
       0,    1,   14,    1, 0x0e /* Public */,
       0,    0,   17,    1, 0x2e /* Public | MethodCloned */,

       0        // eod
};

void QtnPropertyQSizeFCallback::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::CreateInstance) {
        switch (_id) {
        case 0: { QtnPropertyQSizeFCallback *_r = new QtnPropertyQSizeFCallback((*reinterpret_cast< QObject*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        case 1: { QtnPropertyQSizeFCallback *_r = new QtnPropertyQSizeFCallback();
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        default: break;
        }
    }
    Q_UNUSED(_o);
}

const QMetaObject QtnPropertyQSizeFCallback::staticMetaObject = {
    { &QtnSinglePropertyCallback<QtnPropertyQSizeFBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyQSizeFCallback.data,
      qt_meta_data_QtnPropertyQSizeFCallback,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQSizeFCallback::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQSizeFCallback::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQSizeFCallback.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyCallback<QtnPropertyQSizeFBase>::qt_metacast(_clname);
}

int QtnPropertyQSizeFCallback::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyCallback<QtnPropertyQSizeFBase>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyQSizeF_t {
    QByteArrayData data[3];
    char stringdata0[26];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQSizeF_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQSizeF_t qt_meta_stringdata_QtnPropertyQSizeF = {
    {
QT_MOC_LITERAL(0, 0, 17), // "QtnPropertyQSizeF"
QT_MOC_LITERAL(1, 18, 0), // ""
QT_MOC_LITERAL(2, 19, 6) // "parent"

    },
    "QtnPropertyQSizeF\0\0parent"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQSizeF[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       2,   18, // constructors
       0,       // flags
       0,       // signalCount

 // constructors: parameters
    0x80000000 | 1, QMetaType::QObjectStar,    2,
    0x80000000 | 1,

 // constructors: name, argc, parameters, tag, flags
       0,    1,   14,    1, 0x0e /* Public */,
       0,    0,   17,    1, 0x2e /* Public | MethodCloned */,

       0        // eod
};

void QtnPropertyQSizeF::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::CreateInstance) {
        switch (_id) {
        case 0: { QtnPropertyQSizeF *_r = new QtnPropertyQSizeF((*reinterpret_cast< QObject*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        case 1: { QtnPropertyQSizeF *_r = new QtnPropertyQSizeF();
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        default: break;
        }
    }
    Q_UNUSED(_o);
}

const QMetaObject QtnPropertyQSizeF::staticMetaObject = {
    { &QtnSinglePropertyValue<QtnPropertyQSizeFBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyQSizeF.data,
      qt_meta_data_QtnPropertyQSizeF,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQSizeF::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQSizeF::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQSizeF.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyValue<QtnPropertyQSizeFBase>::qt_metacast(_clname);
}

int QtnPropertyQSizeF::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyValue<QtnPropertyQSizeFBase>::qt_metacall(_c, _id, _a);
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
