/****************************************************************************
** Meta object code from reading C++ file 'PropertyQVector3D.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../GUI/PropertyQVector3D.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'PropertyQVector3D.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtnPropertyQVector3DBase_t {
    QByteArrayData data[1];
    char stringdata0[25];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQVector3DBase_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQVector3DBase_t qt_meta_stringdata_QtnPropertyQVector3DBase = {
    {
QT_MOC_LITERAL(0, 0, 24) // "QtnPropertyQVector3DBase"

    },
    "QtnPropertyQVector3DBase"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQVector3DBase[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyQVector3DBase::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyQVector3DBase::staticMetaObject = {
    { &QtnStructPropertyBase<QVector3D,QtnPropertyFloatCallback>::staticMetaObject, qt_meta_stringdata_QtnPropertyQVector3DBase.data,
      qt_meta_data_QtnPropertyQVector3DBase,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQVector3DBase::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQVector3DBase::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQVector3DBase.stringdata0))
        return static_cast<void*>(this);
    return QtnStructPropertyBase<QVector3D,QtnPropertyFloatCallback>::qt_metacast(_clname);
}

int QtnPropertyQVector3DBase::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnStructPropertyBase<QVector3D,QtnPropertyFloatCallback>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyQVector3DCallback_t {
    QByteArrayData data[3];
    char stringdata0[37];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQVector3DCallback_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQVector3DCallback_t qt_meta_stringdata_QtnPropertyQVector3DCallback = {
    {
QT_MOC_LITERAL(0, 0, 28), // "QtnPropertyQVector3DCallback"
QT_MOC_LITERAL(1, 29, 0), // ""
QT_MOC_LITERAL(2, 30, 6) // "parent"

    },
    "QtnPropertyQVector3DCallback\0\0parent"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQVector3DCallback[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       2,   18, // constructors
       0,       // flags
       0,       // signalCount

 // constructors: parameters
    0x80000000 | 1, QMetaType::QObjectStar,    2,
    0x80000000 | 1,

 // constructors: name, argc, parameters, tag, flags
       0,    1,   14,    1, 0x0e /* Public */,
       0,    0,   17,    1, 0x2e /* Public | MethodCloned */,

       0        // eod
};

void QtnPropertyQVector3DCallback::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::CreateInstance) {
        switch (_id) {
        case 0: { QtnPropertyQVector3DCallback *_r = new QtnPropertyQVector3DCallback((*reinterpret_cast< QObject*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        case 1: { QtnPropertyQVector3DCallback *_r = new QtnPropertyQVector3DCallback();
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        default: break;
        }
    }
    Q_UNUSED(_o);
}

const QMetaObject QtnPropertyQVector3DCallback::staticMetaObject = {
    { &QtnSinglePropertyCallback<QtnPropertyQVector3DBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyQVector3DCallback.data,
      qt_meta_data_QtnPropertyQVector3DCallback,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQVector3DCallback::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQVector3DCallback::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQVector3DCallback.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyCallback<QtnPropertyQVector3DBase>::qt_metacast(_clname);
}

int QtnPropertyQVector3DCallback::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyCallback<QtnPropertyQVector3DBase>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyQVector3D_t {
    QByteArrayData data[3];
    char stringdata0[29];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQVector3D_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQVector3D_t qt_meta_stringdata_QtnPropertyQVector3D = {
    {
QT_MOC_LITERAL(0, 0, 20), // "QtnPropertyQVector3D"
QT_MOC_LITERAL(1, 21, 0), // ""
QT_MOC_LITERAL(2, 22, 6) // "parent"

    },
    "QtnPropertyQVector3D\0\0parent"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQVector3D[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       2,   18, // constructors
       0,       // flags
       0,       // signalCount

 // constructors: parameters
    0x80000000 | 1, QMetaType::QObjectStar,    2,
    0x80000000 | 1,

 // constructors: name, argc, parameters, tag, flags
       0,    1,   14,    1, 0x0e /* Public */,
       0,    0,   17,    1, 0x2e /* Public | MethodCloned */,

       0        // eod
};

void QtnPropertyQVector3D::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::CreateInstance) {
        switch (_id) {
        case 0: { QtnPropertyQVector3D *_r = new QtnPropertyQVector3D((*reinterpret_cast< QObject*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        case 1: { QtnPropertyQVector3D *_r = new QtnPropertyQVector3D();
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        default: break;
        }
    }
    Q_UNUSED(_o);
}

const QMetaObject QtnPropertyQVector3D::staticMetaObject = {
    { &QtnSinglePropertyValue<QtnPropertyQVector3DBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyQVector3D.data,
      qt_meta_data_QtnPropertyQVector3D,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQVector3D::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQVector3D::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQVector3D.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyValue<QtnPropertyQVector3DBase>::qt_metacast(_clname);
}

int QtnPropertyQVector3D::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyValue<QtnPropertyQVector3DBase>::qt_metacall(_c, _id, _a);
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
