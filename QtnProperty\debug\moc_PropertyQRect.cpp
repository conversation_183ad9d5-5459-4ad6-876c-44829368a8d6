/****************************************************************************
** Meta object code from reading C++ file 'PropertyQRect.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../Core/PropertyQRect.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'PropertyQRect.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtnPropertyQRectBase_t {
    QByteArrayData data[1];
    char stringdata0[21];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQRectBase_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQRectBase_t qt_meta_stringdata_QtnPropertyQRectBase = {
    {
QT_MOC_LITERAL(0, 0, 20) // "QtnPropertyQRectBase"

    },
    "QtnPropertyQRectBase"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQRectBase[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyQRectBase::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyQRectBase::staticMetaObject = {
    { &QtnStructPropertyBase<QRect,QtnPropertyIntCallback>::staticMetaObject, qt_meta_stringdata_QtnPropertyQRectBase.data,
      qt_meta_data_QtnPropertyQRectBase,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQRectBase::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQRectBase::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQRectBase.stringdata0))
        return static_cast<void*>(this);
    return QtnStructPropertyBase<QRect,QtnPropertyIntCallback>::qt_metacast(_clname);
}

int QtnPropertyQRectBase::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnStructPropertyBase<QRect,QtnPropertyIntCallback>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyQRectCallback_t {
    QByteArrayData data[3];
    char stringdata0[33];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQRectCallback_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQRectCallback_t qt_meta_stringdata_QtnPropertyQRectCallback = {
    {
QT_MOC_LITERAL(0, 0, 24), // "QtnPropertyQRectCallback"
QT_MOC_LITERAL(1, 25, 0), // ""
QT_MOC_LITERAL(2, 26, 6) // "parent"

    },
    "QtnPropertyQRectCallback\0\0parent"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQRectCallback[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       2,   18, // constructors
       0,       // flags
       0,       // signalCount

 // constructors: parameters
    0x80000000 | 1, QMetaType::QObjectStar,    2,
    0x80000000 | 1,

 // constructors: name, argc, parameters, tag, flags
       0,    1,   14,    1, 0x0e /* Public */,
       0,    0,   17,    1, 0x2e /* Public | MethodCloned */,

       0        // eod
};

void QtnPropertyQRectCallback::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::CreateInstance) {
        switch (_id) {
        case 0: { QtnPropertyQRectCallback *_r = new QtnPropertyQRectCallback((*reinterpret_cast< QObject*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        case 1: { QtnPropertyQRectCallback *_r = new QtnPropertyQRectCallback();
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        default: break;
        }
    }
    Q_UNUSED(_o);
}

const QMetaObject QtnPropertyQRectCallback::staticMetaObject = {
    { &QtnSinglePropertyCallback<QtnPropertyQRectBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyQRectCallback.data,
      qt_meta_data_QtnPropertyQRectCallback,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQRectCallback::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQRectCallback::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQRectCallback.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyCallback<QtnPropertyQRectBase>::qt_metacast(_clname);
}

int QtnPropertyQRectCallback::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyCallback<QtnPropertyQRectBase>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyQRect_t {
    QByteArrayData data[3];
    char stringdata0[25];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQRect_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQRect_t qt_meta_stringdata_QtnPropertyQRect = {
    {
QT_MOC_LITERAL(0, 0, 16), // "QtnPropertyQRect"
QT_MOC_LITERAL(1, 17, 0), // ""
QT_MOC_LITERAL(2, 18, 6) // "parent"

    },
    "QtnPropertyQRect\0\0parent"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQRect[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       2,   18, // constructors
       0,       // flags
       0,       // signalCount

 // constructors: parameters
    0x80000000 | 1, QMetaType::QObjectStar,    2,
    0x80000000 | 1,

 // constructors: name, argc, parameters, tag, flags
       0,    1,   14,    1, 0x0e /* Public */,
       0,    0,   17,    1, 0x2e /* Public | MethodCloned */,

       0        // eod
};

void QtnPropertyQRect::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::CreateInstance) {
        switch (_id) {
        case 0: { QtnPropertyQRect *_r = new QtnPropertyQRect((*reinterpret_cast< QObject*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        case 1: { QtnPropertyQRect *_r = new QtnPropertyQRect();
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        default: break;
        }
    }
    Q_UNUSED(_o);
}

const QMetaObject QtnPropertyQRect::staticMetaObject = {
    { &QtnSinglePropertyValue<QtnPropertyQRectBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyQRect.data,
      qt_meta_data_QtnPropertyQRect,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQRect::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQRect::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQRect.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyValue<QtnPropertyQRectBase>::qt_metacast(_clname);
}

int QtnPropertyQRect::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyValue<QtnPropertyQRectBase>::qt_metacall(_c, _id, _a);
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
