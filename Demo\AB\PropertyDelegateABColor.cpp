/*******************************************************************************
Copyright (c) 2012-2016 <PERSON> <<EMAIL>>
Copyright (c) 2019 <PERSON> <<EMAIL>>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*******************************************************************************/

#include "PropertyDelegateABColor.h"
#include "QtnProperty/Delegates/PropertyDelegateFactory.h"
#include "PropertyABColor.h"

void regABColorDelegates()
{
	QtnPropertyDelegateFactory::staticInstance().registerDelegateDefault(
		&QtnPropertyABColor::staticMetaObject,
		&qtnCreateDelegate<QtnPropertyDelegateABColor, QtnPropertyABColor>,
		"LineEditBttn");
}

QtnPropertyDelegateABColor::QtnPropertyDelegateABColor(
	QtnPropertyABColor &owner)
	: QtnPropertyDelegateQColor(owner)
	, m_owner(owner)
{
}

QWidget *QtnPropertyDelegateABColor::createValueEditorImpl(QWidget * /*parent*/,
	const QRect & /*rect*/, QtnInplaceInfo * /*inplaceInfo*/)
{
	m_owner.invokeClick();
	return nullptr;
}
