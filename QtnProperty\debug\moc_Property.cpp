/****************************************************************************
** Meta object code from reading C++ file 'Property.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../Property.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'Property.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtnProperty_t {
    QByteArrayData data[7];
    char stringdata0[80];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnProperty_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnProperty_t qt_meta_stringdata_QtnProperty = {
    {
QT_MOC_LITERAL(0, 0, 11), // "QtnProperty"
QT_MOC_LITERAL(1, 12, 19), // "propertyValueAccept"
QT_MOC_LITERAL(2, 32, 0), // ""
QT_MOC_LITERAL(3, 33, 19), // "QtnPropertyValuePtr"
QT_MOC_LITERAL(4, 53, 13), // "valueToAccept"
QT_MOC_LITERAL(5, 67, 5), // "bool*"
QT_MOC_LITERAL(6, 73, 6) // "accept"

    },
    "QtnProperty\0propertyValueAccept\0\0"
    "QtnPropertyValuePtr\0valueToAccept\0"
    "bool*\0accept"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnProperty[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       1,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,   19,    2, 0x06 /* Public */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3, 0x80000000 | 5,    4,    6,

       0        // eod
};

void QtnProperty::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        QtnProperty *_t = static_cast<QtnProperty *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->propertyValueAccept((*reinterpret_cast< QtnPropertyValuePtr(*)>(_a[1])),(*reinterpret_cast< bool*(*)>(_a[2]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 0:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyValuePtr >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            typedef void (QtnProperty::*_t)(QtnPropertyValuePtr , bool * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtnProperty::propertyValueAccept)) {
                *result = 0;
                return;
            }
        }
    }
}

const QMetaObject QtnProperty::staticMetaObject = {
    { &QtnPropertyBase::staticMetaObject, qt_meta_stringdata_QtnProperty.data,
      qt_meta_data_QtnProperty,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnProperty::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnProperty::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnProperty.stringdata0))
        return static_cast<void*>(this);
    return QtnPropertyBase::qt_metacast(_clname);
}

int QtnProperty::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnPropertyBase::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 1)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 1;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 1)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 1;
    }
    return _id;
}

// SIGNAL 0
void QtnProperty::propertyValueAccept(QtnPropertyValuePtr _t1, bool * _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
