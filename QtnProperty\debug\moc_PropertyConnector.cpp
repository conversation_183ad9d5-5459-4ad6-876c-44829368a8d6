/****************************************************************************
** Meta object code from reading C++ file 'PropertyConnector.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../PropertyConnector.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'PropertyConnector.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtnPropertyConnector_t {
    QByteArrayData data[7];
    char stringdata0[108];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyConnector_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyConnector_t qt_meta_stringdata_QtnPropertyConnector = {
    {
QT_MOC_LITERAL(0, 0, 20), // "QtnPropertyConnector"
QT_MOC_LITERAL(1, 21, 14), // "onValueChanged"
QT_MOC_LITERAL(2, 36, 0), // ""
QT_MOC_LITERAL(3, 37, 20), // "onModifiedSetChanged"
QT_MOC_LITERAL(4, 58, 22), // "onPropertyStateChanged"
QT_MOC_LITERAL(5, 81, 13), // "QMetaProperty"
QT_MOC_LITERAL(6, 95, 12) // "metaProperty"

    },
    "QtnPropertyConnector\0onValueChanged\0"
    "\0onModifiedSetChanged\0onPropertyStateChanged\0"
    "QMetaProperty\0metaProperty"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyConnector[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       3,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   29,    2, 0x08 /* Private */,
       3,    0,   30,    2, 0x08 /* Private */,
       4,    1,   31,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 5,    6,

       0        // eod
};

void QtnPropertyConnector::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        QtnPropertyConnector *_t = static_cast<QtnPropertyConnector *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->onValueChanged(); break;
        case 1: _t->onModifiedSetChanged(); break;
        case 2: _t->onPropertyStateChanged((*reinterpret_cast< const QMetaProperty(*)>(_a[1]))); break;
        default: ;
        }
    }
}

const QMetaObject QtnPropertyConnector::staticMetaObject = {
    { &QObject::staticMetaObject, qt_meta_stringdata_QtnPropertyConnector.data,
      qt_meta_data_QtnPropertyConnector,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyConnector::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyConnector::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyConnector.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int QtnPropertyConnector::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 3)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 3)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 3;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
