<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
<Qt_DEFINES_>_WINDOWS;WIN32;WIN64;QT_NO_DEBUG;QT_WIDGETS_LIB;QT_GUI_LIB;QT_SCRIPT_LIB;QT_CORE_LIB;NDEBUG</Qt_DEFINES_>
<Qt_INCLUDEPATH_>D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include;D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtWidgets;D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtGui;D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtANGLE;D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtScript;D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtCore;D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\mkspecs\win32-msvc</Qt_INCLUDEPATH_>
<Qt_STDCPP_></Qt_STDCPP_>
<Qt_RUNTIME_>MultiThreadedDLL</Qt_RUNTIME_>
<Qt_CL_OPTIONS_>-Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding</Qt_CL_OPTIONS_>
<Qt_LIBS_>D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\lib\qtmain.lib;shell32.lib;D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\lib\Qt5Widgets.lib;D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\lib\Qt5Gui.lib;D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\lib\Qt5Script.lib;D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\lib\Qt5Core.lib</Qt_LIBS_>
<Qt_LINK_OPTIONS_>"/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='*******' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'"</Qt_LINK_OPTIONS_>
<QMake_QT_SYSROOT_></QMake_QT_SYSROOT_>
<QMake_QT_INSTALL_PREFIX_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64</QMake_QT_INSTALL_PREFIX_>
<QMake_QT_INSTALL_ARCHDATA_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64</QMake_QT_INSTALL_ARCHDATA_>
<QMake_QT_INSTALL_DATA_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64</QMake_QT_INSTALL_DATA_>
<QMake_QT_INSTALL_DOCS_>D:/Qt/Qt5.9.9/Docs/Qt-5.9.9</QMake_QT_INSTALL_DOCS_>
<QMake_QT_INSTALL_HEADERS_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/include</QMake_QT_INSTALL_HEADERS_>
<QMake_QT_INSTALL_LIBS_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/lib</QMake_QT_INSTALL_LIBS_>
<QMake_QT_INSTALL_LIBEXECS_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/bin</QMake_QT_INSTALL_LIBEXECS_>
<QMake_QT_INSTALL_BINS_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/bin</QMake_QT_INSTALL_BINS_>
<QMake_QT_INSTALL_TESTS_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/tests</QMake_QT_INSTALL_TESTS_>
<QMake_QT_INSTALL_PLUGINS_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/plugins</QMake_QT_INSTALL_PLUGINS_>
<QMake_QT_INSTALL_IMPORTS_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/imports</QMake_QT_INSTALL_IMPORTS_>
<QMake_QT_INSTALL_QML_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/qml</QMake_QT_INSTALL_QML_>
<QMake_QT_INSTALL_TRANSLATIONS_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/translations</QMake_QT_INSTALL_TRANSLATIONS_>
<QMake_QT_INSTALL_CONFIGURATION_></QMake_QT_INSTALL_CONFIGURATION_>
<QMake_QT_INSTALL_EXAMPLES_>D:/Qt/Qt5.9.9/Examples/Qt-5.9.9</QMake_QT_INSTALL_EXAMPLES_>
<QMake_QT_INSTALL_DEMOS_>D:/Qt/Qt5.9.9/Examples/Qt-5.9.9</QMake_QT_INSTALL_DEMOS_>
<QMake_QT_HOST_PREFIX_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64</QMake_QT_HOST_PREFIX_>
<QMake_QT_HOST_DATA_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64</QMake_QT_HOST_DATA_>
<QMake_QT_HOST_BINS_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/bin</QMake_QT_HOST_BINS_>
<QMake_QT_HOST_LIBS_>D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/lib</QMake_QT_HOST_LIBS_>
<QMake_QMAKE_SPEC_>win32-msvc</QMake_QMAKE_SPEC_>
<QMake_QMAKE_XSPEC_>win32-msvc</QMake_QMAKE_XSPEC_>
<QMake_QMAKE_VERSION_>3.1</QMake_QMAKE_VERSION_>
<QMake_QT_VERSION_>5.9.9</QMake_QT_VERSION_>
<QtBkup_QtHash>jZFBTgMxDEWv4hN0AFGkLrpIbZMaMklke4oqhOb+t2hmNGgkNmSRTfLft//PtxBnF7+fj4fT4TSPdsOXp+fj/PYKWJQhTgKGKtXhSyiyG1wkr4f4XTLbeahaPhh9EOdxvRSXkqOWqQ6YsIxVEjcVNyGyWdFdZSAZ00Rcg197rQLROiKkDSbRtkBRYQNzwlp7rVLIcQqRzUOmoAQ6tT5G7uU3eZKLBr0DprnUJX13LXuWDYTm9Q/dYn8OO9jK49z+EZf4y1vfDn9cfscrJw7GPw8=</QtBkup_QtHash>
    <QtVersion>5.9.9</QtVersion>
    <QtVersionMajor>5</QtVersionMajor>
    <QtVersionMinor>9</QtVersionMinor>
    <QtVersionPatch>9</QtVersionPatch>
  </PropertyGroup>
</Project>
