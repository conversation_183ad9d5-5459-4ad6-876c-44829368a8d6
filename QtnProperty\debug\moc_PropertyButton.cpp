/****************************************************************************
** Meta object code from reading C++ file 'PropertyButton.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../GUI/PropertyButton.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'PropertyButton.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtnPropertyButton_t {
    QByteArrayData data[8];
    char stringdata0[100];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyButton_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyButton_t qt_meta_stringdata_QtnPropertyButton = {
    {
QT_MOC_LITERAL(0, 0, 17), // "QtnPropertyButton"
QT_MOC_LITERAL(1, 18, 5), // "click"
QT_MOC_LITERAL(2, 24, 0), // ""
QT_MOC_LITERAL(3, 25, 24), // "const QtnPropertyButton*"
QT_MOC_LITERAL(4, 50, 8), // "property"
QT_MOC_LITERAL(5, 59, 13), // "preDrawButton"
QT_MOC_LITERAL(6, 73, 19), // "QStyleOptionButton*"
QT_MOC_LITERAL(7, 93, 6) // "option"

    },
    "QtnPropertyButton\0click\0\0"
    "const QtnPropertyButton*\0property\0"
    "preDrawButton\0QStyleOptionButton*\0"
    "option"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyButton[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       2,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   24,    2, 0x06 /* Public */,
       5,    2,   27,    2, 0x06 /* Public */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 3, 0x80000000 | 6,    4,    7,

       0        // eod
};

void QtnPropertyButton::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        QtnPropertyButton *_t = static_cast<QtnPropertyButton *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->click((*reinterpret_cast< const QtnPropertyButton*(*)>(_a[1]))); break;
        case 1: _t->preDrawButton((*reinterpret_cast< const QtnPropertyButton*(*)>(_a[1])),(*reinterpret_cast< QStyleOptionButton*(*)>(_a[2]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            typedef void (QtnPropertyButton::*_t)(const QtnPropertyButton * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtnPropertyButton::click)) {
                *result = 0;
                return;
            }
        }
        {
            typedef void (QtnPropertyButton::*_t)(const QtnPropertyButton * , QStyleOptionButton * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtnPropertyButton::preDrawButton)) {
                *result = 1;
                return;
            }
        }
    }
}

const QMetaObject QtnPropertyButton::staticMetaObject = {
    { &QtnProperty::staticMetaObject, qt_meta_stringdata_QtnPropertyButton.data,
      qt_meta_data_QtnPropertyButton,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyButton::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyButton::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyButton.stringdata0))
        return static_cast<void*>(this);
    return QtnProperty::qt_metacast(_clname);
}

int QtnPropertyButton::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnProperty::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 2)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 2;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 2)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 2;
    }
    return _id;
}

// SIGNAL 0
void QtnPropertyButton::click(const QtnPropertyButton * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void QtnPropertyButton::preDrawButton(const QtnPropertyButton * _t1, QStyleOptionButton * _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
