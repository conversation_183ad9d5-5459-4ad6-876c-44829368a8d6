﻿Build started 8/7/2025 3:50:07 PM.
Logging verbosity is set to: Normal.     1>Project "G:\Code\Qt\QtnProperty\QtnProperty\QtnProperty.vcxproj" on node 2 (Build target(s)).
     1>InitializeBuildStatus:
         Creating "debug\QtnProperty.tlog\unsuccessfulbuild" because "AlwaysCreate" was specified.
         Touching "debug\QtnProperty.tlog\unsuccessfulbuild".
       CustomBuild:
         Generate moc_predefs.h
       QtPrepare:
          Qt/MSBuild: *******
         Creating directory "debug\qt\".
       QtVars:
         WHERE /Q cl.exe
         
         ==== HostExec: Microsoft.Build.Tasks.Exec
         WorkingDirectory: G:\Code\Qt\QtnProperty\QtnProperty\debug\qt\qmake
         Command: ("D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/bin/qmake" -query) 1> props.txt
         ("D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/bin/qmake" -query) 1> props.txt
         == OK ExitCode: 0
         
         Reading Qt configuration (D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/bin/qmake)
         
         ==== HostExec: Microsoft.Build.Tasks.Exec
         WorkingDirectory: G:\Code\Qt\QtnProperty\QtnProperty\debug\qt\qmake
         Command: ("D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/bin/qmake"  -tp vc "CONFIG -= debug release debug_and_release" "CONFIG += debug warn_off"  qtvars.pro) 1> qtvars.log 2>&1
         ("D:/Qt/Qt5.9.9/5.9.9/msvc2015_64/bin/qmake"  -tp vc "CONFIG -= debug release debug_and_release" "CONFIG += debug warn_off"  qtvars.pro) 1> qtvars.log 2>&1
         == OK ExitCode: 0
         
         Copying file from "G:\Code\Qt\QtnProperty\QtnProperty\debug\qt\qmake\qtvars.xml" to "G:\Code\Qt\QtnProperty\QtnProperty\debug\qt\qtvars.xml".
       QtVarsLoad:
          Qt: 5.9.9
       QtRccFilterSetModified:
       Skipping target "QtRccFilterSetModified" because it has no outputs.
       QtRccFilter:
       Skipping target "QtRccFilter" because it has no outputs.
       QtQmlSetModified:
       Skipping target "QtQmlSetModified" because it has no outputs.
       QtQmlCacheItem:
       Skipping target "QtQmlCacheItem" because it has no outputs.
       QtWork:
         Uic'ing CustomPropertyEditorDialog.ui...
         Uic'ing CustomPropertyOptionsDialog.ui...
         Uic'ing Utils\MultilineTextDialog.ui...
         Rcc'ing QtnProperty.qrc...
         Moc'ing Utils\AccessibilityProxy.h...
         Moc'ing CustomPropertyEditorDialog.h...
         Moc'ing CustomPropertyOptionsDialog.h...
         Moc'ing CustomPropertyWidget.h...
         Moc'ing MultiProperty.h...
         Moc'ing Utils\MultilineTextDialog.h...
         Moc'ing Property.h...
         Moc'ing PropertyBase.h...
         Moc'ing Core\PropertyBool.h...
         Moc'ing GUI\PropertyButton.h...
         Moc'ing PropertyConnector.h...
         Moc'ing Core\PropertyDouble.h...
         Moc'ing Core\PropertyEnum.h...
         Moc'ing Core\PropertyEnumFlags.h...
         Moc'ing Core\PropertyFloat.h...
         Moc'ing Core\PropertyInt.h...
         Moc'ing PropertyInt64.h...
         Moc'ing GUI\PropertyQBrush.h...
         Moc'ing GUI\PropertyQColor.h...
         Moc'ing GUI\PropertyQFont.h...
         Moc'ing PropertyQKeySequence.h...
         Moc'ing GUI\PropertyQPen.h...
         Moc'ing Core\PropertyQPoint.h...
         Moc'ing Core\PropertyQPointF.h...
         Moc'ing Core\PropertyQRect.h...
         Moc'ing Core\PropertyQRectF.h...
         Moc'ing Core\PropertyQSize.h...
         Moc'ing Core\PropertyQSizeF.h...
         Moc'ing Core\PropertyQString.h...
         Moc'ing PropertyQVariant.h...
         Moc'ing GUI\PropertyQVector3D.h...
         Moc'ing PropertySet.h...
         Moc'ing Core\PropertyUInt.h...
         Moc'ing PropertyUInt64.h...
         Moc'ing PropertyView.h...
         Moc'ing PropertyWidget.h...
         Moc'ing PropertyWidgetEx.h...
         Moc'ing QObjectPropertyWidget.h...
         Moc'ing Utils\QtnCompleterItemDelegate.h...
         Moc'ing Utils\QtnCompleterLineEdit.h...
         Moc'ing Utils\QtnInt64SpinBox.h...
         Moc'ing VarProperty.h...
       ClCompile:
         D:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\bin\amd64\CL.exe /c /IDebug /IG:\Code\Qt\QtnProperty\QtnProperty /ID:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include /ID:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtWidgets /ID:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtGui /ID:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtANGLE /ID:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtScript /ID:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtCore /I"D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\mkspecs\win32-msvc" /IG:\Code\Qt\QtnProperty\QtnProperty\ /IGeneratedFiles\Debug /IGeneratedFiles /I. /I..\. /Idebug /I"C:\Program Files (x86)\Visual Leak Detector\include" /Zi /nologo /W3 /WX- /MP /Od /D _WINDOWS /D WIN32 /D WIN64 /D QT_WIDGETS_LIB /D QT_GUI_LIB /D QT_SCRIPT_LIB /D QT_CORE_LIB /D _WINDOWS /D UNICODE /D _UNICODE /D WIN32 /D WIN64 /D _CRT_SECURE_NO_WARNINGS /D DEBUG /Gm- /EHsc /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"debug\\" /Fd"debug\QtnProperty.pdb" /Gd /TP /wd4577 /wd4467 /errorReport:prompt -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 Utils\AccessibilityProxy.cpp CustomPropertyEditorDialog.cpp CustomPropertyOptionsDialog.cpp CustomPropertyWidget.cpp Utils\DoubleSpinBox.cpp Enum.cpp Utils\InplaceEditing.cpp Install.cpp MultiProperty.cpp Utils\MultilineTextDialog.cpp Property.cpp PropertyBase.cpp Core\PropertyBool.cpp GUI\PropertyButton.cpp PropertyConnector.cpp Delegates\PropertyDelegate.cpp Delegates\PropertyDelegateAux.cpp Delegates\Core\PropertyDelegateBool.cpp Delegates\GUI\PropertyDelegateButton.cpp Delegates\Core\PropertyDelegateDouble.cpp Delegates\Core\PropertyDelegateEnum.cpp Delegates\Core\PropertyDelegateEnumFlags.cpp Delegates\PropertyDelegateFactory.cpp Delegates\Core\PropertyDelegateFloat.cpp Delegates\Utils\PropertyDelegateGeoCoord.cpp Delegates\Utils\PropertyDelegateGeoPoint.cpp Auxiliary\PropertyDelegateInfo.cpp Delegates\Core\PropertyDelegateInt.cpp PropertyDelegateMetaEnum.cpp Delegates\Utils\PropertyDelegateMisc.cpp Delegates\Utils\PropertyDelegatePropertySet.cpp Delegates\GUI\PropertyDelegateQBrush.cpp Delegates\GUI\PropertyDelegateQColor.cpp Delegates\GUI\PropertyDelegateQFont.cpp Delegates\GUI\PropertyDelegateQPen.cpp Delegates\Core\PropertyDelegateQPoint.cpp Delegates\Core\PropertyDelegateQPointF.cpp Delegates\Core\PropertyDelegateQRect.cpp Delegates\Core\PropertyDelegateQRectF.cpp Delegates\Core\PropertyDelegateQSize.cpp Delegates\Core\PropertyDelegateQSizeF.cpp Delegates\Core\PropertyDelegateQString.cpp Delegates\GUI\PropertyDelegateQVector3D.cpp Delegates\Utils\PropertyDelegateSliderBox.cpp Delegates\Core\PropertyDelegateUInt.cpp Core\PropertyDouble.cpp Delegates\Utils\PropertyEditorAux.cpp Delegates\Utils\PropertyEditorHandler.cpp Core\PropertyEnum.cpp Core\PropertyEnumFlags.cpp Core\PropertyFloat.cpp Core\PropertyInt.cpp PropertyInt64.cpp GUI\PropertyQBrush.cpp GUI\PropertyQColor.cpp GUI\PropertyQFont.cpp PropertyQKeySequence.cpp GUI\PropertyQPen.cpp Core\PropertyQPoint.cpp Core\PropertyQPointF.cpp Core\PropertyQRect.cpp Core\PropertyQRectF.cpp Core\PropertyQSize.cpp Core\PropertyQSizeF.cpp Core\PropertyQString.cpp PropertyQVariant.cpp GUI\PropertyQVector3D.cpp PropertySet.cpp Core\PropertyUInt.cpp PropertyUInt64.cpp PropertyView.cpp PropertyWidget.cpp PropertyWidgetEx.cpp QObjectPropertySet.cpp QObjectPropertyWidget.cpp Utils\QtnCompleterItemDelegate.cpp Utils\QtnCompleterLineEdit.cpp Utils\QtnConnections.cpp Utils\QtnInt64SpinBox.cpp VarProperty.cpp
         AccessibilityProxy.cpp
         CustomPropertyEditorDialog.cpp
         CustomPropertyOptionsDialog.cpp
         CustomPropertyWidget.cpp
         DoubleSpinBox.cpp
         Enum.cpp
         InplaceEditing.cpp
         Install.cpp
         MultiProperty.cpp
         MultilineTextDialog.cpp
         Property.cpp
         PropertyBase.cpp
         PropertyBool.cpp
         PropertyButton.cpp
         PropertyConnector.cpp
         PropertyDelegate.cpp
         PropertyDelegateAux.cpp
         PropertyDelegateBool.cpp
         PropertyDelegateButton.cpp
         PropertyDelegateDouble.cpp
         PropertyDelegateEnum.cpp
         PropertyDelegateEnumFlags.cpp
         PropertyDelegateFactory.cpp
         PropertyDelegateFloat.cpp
         PropertyDelegateGeoCoord.cpp
         PropertyDelegateGeoPoint.cpp
         PropertyDelegateInfo.cpp
         PropertyDelegateInt.cpp
         PropertyDelegateMetaEnum.cpp
         PropertyDelegateMisc.cpp
         PropertyDelegatePropertySet.cpp
         PropertyDelegateQBrush.cpp
         PropertyDelegateQColor.cpp
         PropertyDelegateQFont.cpp
         PropertyDelegateQPen.cpp
         PropertyDelegateQPoint.cpp
         PropertyDelegateQPointF.cpp
         PropertyDelegateQRect.cpp
         PropertyDelegateQRectF.cpp
         PropertyDelegateQSize.cpp
         PropertyDelegateQSizeF.cpp
         PropertyDelegateQString.cpp
         PropertyDelegateQVector3D.cpp
         PropertyDelegateSliderBox.cpp
         PropertyDelegateUInt.cpp
         PropertyDouble.cpp
         PropertyEditorAux.cpp
         PropertyEditorHandler.cpp
         PropertyEnum.cpp
         PropertyEnumFlags.cpp
         PropertyFloat.cpp
         PropertyInt.cpp
         PropertyInt64.cpp
         PropertyQBrush.cpp
         PropertyQColor.cpp
         PropertyQFont.cpp
         PropertyQKeySequence.cpp
         PropertyQPen.cpp
         PropertyQPoint.cpp
         PropertyQPointF.cpp
         PropertyQRect.cpp
         PropertyQRectF.cpp
         PropertyQSize.cpp
         PropertyQSizeF.cpp
         PropertyQString.cpp
         PropertyQVariant.cpp
         PropertyQVector3D.cpp
         PropertySet.cpp
         PropertyUInt.cpp
         PropertyUInt64.cpp
         PropertyView.cpp
         PropertyWidget.cpp
         PropertyWidgetEx.cpp
         QObjectPropertySet.cpp
         QObjectPropertyWidget.cpp
         QtnCompleterItemDelegate.cpp
         QtnCompleterLineEdit.cpp
         QtnConnections.cpp
         QtnInt64SpinBox.cpp
         VarProperty.cpp
         D:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\bin\amd64\CL.exe /c /IDebug /IG:\Code\Qt\QtnProperty\QtnProperty /ID:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include /ID:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtWidgets /ID:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtGui /ID:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtANGLE /ID:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtScript /ID:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtCore /I"D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\mkspecs\win32-msvc" /IG:\Code\Qt\QtnProperty\QtnProperty\ /IGeneratedFiles\Debug /IGeneratedFiles /I. /I..\. /Idebug /I"C:\Program Files (x86)\Visual Leak Detector\include" /Zi /nologo /W3 /WX- /MP /Od /D _WINDOWS /D WIN32 /D WIN64 /D QT_WIDGETS_LIB /D QT_GUI_LIB /D QT_SCRIPT_LIB /D QT_CORE_LIB /D _WINDOWS /D UNICODE /D _UNICODE /D WIN32 /D WIN64 /D _CRT_SECURE_NO_WARNINGS /D DEBUG /Gm- /EHsc /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"debug\\" /Fd"debug\QtnProperty.pdb" /Gd /TP /wd4577 /wd4467 /wd4711 /errorReport:prompt -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 Debug\qrc_QtnProperty.cpp
         qrc_QtnProperty.cpp
         D:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\bin\amd64\CL.exe /c /IDebug /IG:\Code\Qt\QtnProperty\QtnProperty /ID:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include /ID:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtWidgets /ID:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtGui /ID:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtANGLE /ID:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtScript /ID:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtCore /I"D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\mkspecs\win32-msvc" /IG:\Code\Qt\QtnProperty\QtnProperty\ /IGeneratedFiles\Debug /IGeneratedFiles /I. /I..\. /Idebug /I"C:\Program Files (x86)\Visual Leak Detector\include" /Zi /nologo /W3 /WX- /MP /Od /D _WINDOWS /D WIN32 /D WIN64 /D QT_WIDGETS_LIB /D QT_GUI_LIB /D QT_SCRIPT_LIB /D QT_CORE_LIB /D _WINDOWS /D UNICODE /D _UNICODE /D WIN32 /D WIN64 /D _CRT_SECURE_NO_WARNINGS /D DEBUG /Gm- /EHsc /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"debug\\" /Fd"debug\QtnProperty.pdb" /Gd /TP /wd4577 /wd4467 /errorReport:prompt -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -w34100 -w34189 -w44996 -w44456 -w44457 -w44458  -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 Debug\moc_AccessibilityProxy.cpp Debug\moc_CustomPropertyEditorDialog.cpp Debug\moc_CustomPropertyOptionsDialog.cpp Debug\moc_CustomPropertyWidget.cpp Debug\moc_MultiProperty.cpp Debug\moc_MultilineTextDialog.cpp Debug\moc_Property.cpp Debug\moc_PropertyBase.cpp Debug\moc_PropertyBool.cpp Debug\moc_PropertyButton.cpp Debug\moc_PropertyConnector.cpp Debug\moc_PropertyDouble.cpp Debug\moc_PropertyEnum.cpp Debug\moc_PropertyEnumFlags.cpp Debug\moc_PropertyFloat.cpp Debug\moc_PropertyInt.cpp Debug\moc_PropertyInt64.cpp Debug\moc_PropertyQBrush.cpp Debug\moc_PropertyQColor.cpp Debug\moc_PropertyQFont.cpp Debug\moc_PropertyQKeySequence.cpp Debug\moc_PropertyQPen.cpp Debug\moc_PropertyQPoint.cpp Debug\moc_PropertyQPointF.cpp Debug\moc_PropertyQRect.cpp Debug\moc_PropertyQRectF.cpp Debug\moc_PropertyQSize.cpp Debug\moc_PropertyQSizeF.cpp Debug\moc_PropertyQString.cpp Debug\moc_PropertyQVariant.cpp Debug\moc_PropertyQVector3D.cpp Debug\moc_PropertySet.cpp Debug\moc_PropertyUInt.cpp Debug\moc_PropertyUInt64.cpp Debug\moc_PropertyView.cpp Debug\moc_PropertyWidget.cpp Debug\moc_PropertyWidgetEx.cpp Debug\moc_QObjectPropertyWidget.cpp Debug\moc_QtnCompleterItemDelegate.cpp Debug\moc_QtnCompleterLineEdit.cpp Debug\moc_QtnInt64SpinBox.cpp Debug\moc_VarProperty.cpp
         moc_AccessibilityProxy.cpp
         moc_CustomPropertyEditorDialog.cpp
         moc_CustomPropertyOptionsDialog.cpp
         moc_CustomPropertyWidget.cpp
         moc_MultiProperty.cpp
         moc_MultilineTextDialog.cpp
         moc_Property.cpp
         moc_PropertyBase.cpp
         moc_PropertyBool.cpp
         moc_PropertyButton.cpp
         moc_PropertyConnector.cpp
         moc_PropertyDouble.cpp
         moc_PropertyEnum.cpp
         moc_PropertyEnumFlags.cpp
         moc_PropertyFloat.cpp
         moc_PropertyInt.cpp
         moc_PropertyInt64.cpp
         moc_PropertyQBrush.cpp
         moc_PropertyQColor.cpp
         moc_PropertyQFont.cpp
         moc_PropertyQKeySequence.cpp
         moc_PropertyQPen.cpp
         moc_PropertyQPoint.cpp
         moc_PropertyQPointF.cpp
         moc_PropertyQRect.cpp
         moc_PropertyQRectF.cpp
         moc_PropertyQSize.cpp
         moc_PropertyQSizeF.cpp
         moc_PropertyQString.cpp
         moc_PropertyQVariant.cpp
         moc_PropertyQVector3D.cpp
         moc_PropertySet.cpp
         moc_PropertyUInt.cpp
         moc_PropertyUInt64.cpp
         moc_PropertyView.cpp
         moc_PropertyWidget.cpp
         moc_PropertyWidgetEx.cpp
         moc_QObjectPropertyWidget.cpp
         moc_QtnCompleterItemDelegate.cpp
         moc_QtnCompleterLineEdit.cpp
         moc_QtnInt64SpinBox.cpp
         moc_VarProperty.cpp
       Lib:
         D:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\bin\amd64\Lib.exe /OUT:"..\bin-win32-msvc1944-x86_64\debug\\QtnProperty.lib" /LIBPATH:"C:\Program Files (x86)\Visual Leak Detector\lib\Win64" /NOLOGO /MACHINE:X64 debug\AccessibilityProxy.obj
         debug\CustomPropertyEditorDialog.obj
         debug\CustomPropertyOptionsDialog.obj
         debug\CustomPropertyWidget.obj
         debug\DoubleSpinBox.obj
         debug\Enum.obj
         debug\InplaceEditing.obj
         debug\Install.obj
         debug\MultiProperty.obj
         debug\MultilineTextDialog.obj
         debug\Property.obj
         debug\PropertyBase.obj
         debug\PropertyBool.obj
         debug\PropertyButton.obj
         debug\PropertyConnector.obj
         debug\PropertyDelegate.obj
         debug\PropertyDelegateAux.obj
         debug\PropertyDelegateBool.obj
         debug\PropertyDelegateButton.obj
         debug\PropertyDelegateDouble.obj
         debug\PropertyDelegateEnum.obj
         debug\PropertyDelegateEnumFlags.obj
         debug\PropertyDelegateFactory.obj
         debug\PropertyDelegateFloat.obj
         debug\PropertyDelegateGeoCoord.obj
         debug\PropertyDelegateGeoPoint.obj
         debug\PropertyDelegateInfo.obj
         debug\PropertyDelegateInt.obj
         debug\PropertyDelegateMetaEnum.obj
         debug\PropertyDelegateMisc.obj
         debug\PropertyDelegatePropertySet.obj
         debug\PropertyDelegateQBrush.obj
         debug\PropertyDelegateQColor.obj
         debug\PropertyDelegateQFont.obj
         debug\PropertyDelegateQPen.obj
         debug\PropertyDelegateQPoint.obj
         debug\PropertyDelegateQPointF.obj
         debug\PropertyDelegateQRect.obj
         debug\PropertyDelegateQRectF.obj
         debug\PropertyDelegateQSize.obj
         debug\PropertyDelegateQSizeF.obj
         debug\PropertyDelegateQString.obj
         debug\PropertyDelegateQVector3D.obj
         debug\PropertyDelegateSliderBox.obj
         debug\PropertyDelegateUInt.obj
         debug\PropertyDouble.obj
         debug\PropertyEditorAux.obj
         debug\PropertyEditorHandler.obj
         debug\PropertyEnum.obj
         debug\PropertyEnumFlags.obj
         debug\PropertyFloat.obj
         debug\PropertyInt.obj
         debug\PropertyInt64.obj
         debug\PropertyQBrush.obj
         debug\PropertyQColor.obj
         debug\PropertyQFont.obj
         debug\PropertyQKeySequence.obj
         debug\PropertyQPen.obj
         debug\PropertyQPoint.obj
         debug\PropertyQPointF.obj
         debug\PropertyQRect.obj
         debug\PropertyQRectF.obj
         debug\PropertyQSize.obj
         debug\PropertyQSizeF.obj
         debug\PropertyQString.obj
         debug\PropertyQVariant.obj
         debug\PropertyQVector3D.obj
         debug\PropertySet.obj
         debug\PropertyUInt.obj
         debug\PropertyUInt64.obj
         debug\PropertyView.obj
         debug\PropertyWidget.obj
         debug\PropertyWidgetEx.obj
         debug\QObjectPropertySet.obj
         debug\QObjectPropertyWidget.obj
         debug\QtnCompleterItemDelegate.obj
         debug\QtnCompleterLineEdit.obj
         debug\QtnConnections.obj
         debug\QtnInt64SpinBox.obj
         debug\VarProperty.obj
         debug\qrc_QtnProperty.obj
         debug\moc_AccessibilityProxy.obj
         debug\moc_CustomPropertyEditorDialog.obj
         debug\moc_CustomPropertyOptionsDialog.obj
         debug\moc_CustomPropertyWidget.obj
         debug\moc_MultiProperty.obj
         debug\moc_MultilineTextDialog.obj
         debug\moc_Property.obj
         debug\moc_PropertyBase.obj
         debug\moc_PropertyBool.obj
         debug\moc_PropertyButton.obj
         debug\moc_PropertyConnector.obj
         debug\moc_PropertyDouble.obj
         debug\moc_PropertyEnum.obj
         debug\moc_PropertyEnumFlags.obj
         debug\moc_PropertyFloat.obj
         debug\moc_PropertyInt.obj
         debug\moc_PropertyInt64.obj
         debug\moc_PropertyQBrush.obj
         debug\moc_PropertyQColor.obj
         debug\moc_PropertyQFont.obj
         debug\moc_PropertyQKeySequence.obj
         debug\moc_PropertyQPen.obj
         debug\moc_PropertyQPoint.obj
         debug\moc_PropertyQPointF.obj
         debug\moc_PropertyQRect.obj
         debug\moc_PropertyQRectF.obj
         debug\moc_PropertyQSize.obj
         debug\moc_PropertyQSizeF.obj
         debug\moc_PropertyQString.obj
         debug\moc_PropertyQVariant.obj
         debug\moc_PropertyQVector3D.obj
         debug\moc_PropertySet.obj
         debug\moc_PropertyUInt.obj
         debug\moc_PropertyUInt64.obj
         debug\moc_PropertyView.obj
         debug\moc_PropertyWidget.obj
         debug\moc_PropertyWidgetEx.obj
         debug\moc_QObjectPropertyWidget.obj
         debug\moc_QtnCompleterItemDelegate.obj
         debug\moc_QtnCompleterLineEdit.obj
         debug\moc_QtnInt64SpinBox.obj
         debug\moc_VarProperty.obj
         QtnProperty.vcxproj -> G:\Code\Qt\QtnProperty\QtnProperty\..\bin-win32-msvc1944-x86_64\debug\QtnProperty.lib
       CopyFilesToOutputDirectory:
         Copying file from "G:\Code\Qt\QtnProperty\QtnProperty\debug\QtnProperty.pdb" to "G:\Code\Qt\QtnProperty\bin-win32-msvc1944-x86_64\debug\QtnProperty.pdb".
       FinalizeBuildStatus:
         Deleting file "debug\QtnProperty.tlog\unsuccessfulbuild".
         Touching "debug\QtnProperty.tlog\QtnProperty.lastbuildstate".
     1>Done Building Project "G:\Code\Qt\QtnProperty\QtnProperty\QtnProperty.vcxproj" (Build target(s)).

Build succeeded.
    0 Warning(s)
    0 Error(s)

Time Elapsed 00:00:19.78
