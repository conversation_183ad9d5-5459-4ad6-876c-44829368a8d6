<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CustomPropertyOptionsDialog</class>
 <widget class="QDialog" name="CustomPropertyOptionsDialog">
  <property name="windowModality">
   <enum>Qt::NonModal</enum>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>221</width>
    <height>291</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string notr="true"/>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <property name="spacing">
    <number>9</number>
   </property>
   <property name="leftMargin">
    <number>9</number>
   </property>
   <property name="topMargin">
    <number>9</number>
   </property>
   <property name="rightMargin">
    <number>9</number>
   </property>
   <property name="bottomMargin">
    <number>9</number>
   </property>
   <item>
    <widget class="QLabel" name="label">
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>17</height>
      </size>
     </property>
     <property name="text">
      <string>Name:</string>
     </property>
     <property name="margin">
      <number>2</number>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="widget" native="true">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>21</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>21</height>
      </size>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QSpinBox" name="editIndex"/>
      </item>
      <item>
       <widget class="QLineEdit" name="editName"/>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="typeBox">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>141</height>
      </size>
     </property>
     <property name="title">
      <string>Type</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <widget class="QRadioButton" name="rbNumeric">
        <property name="text">
         <string>Numeric</string>
        </property>
        <property name="checked">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QRadioButton" name="rbString">
        <property name="text">
         <string>String</string>
        </property>
        <property name="checked">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QRadioButton" name="rbBoolean">
        <property name="text">
         <string>Boolean</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QRadioButton" name="rbDictionary">
        <property name="text">
         <string>Dictionary</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QRadioButton" name="rbList">
        <property name="text">
         <string>List</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QRadioButton" name="rbNull">
        <property name="text">
         <string>Null</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
     <property name="centerButtons">
      <bool>true</bool>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
