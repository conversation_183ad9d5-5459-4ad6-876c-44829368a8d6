﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Form Files">
      <UniqueIdentifier>{99349809-55BA-4b9d-BF79-8FDBB0286EB3}</UniqueIdentifier>
      <Extensions>ui</Extensions>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="Form Files">
      <UniqueIdentifier>{99349809-55BA-4b9d-BF79-8FDBB0286EB3}</UniqueIdentifier>
      <Extensions>ui</Extensions>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="Generated Files">
      <UniqueIdentifier>{71ED8ED8-ACB9-4CE9-BBE1-E00B30144E11}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;moc;h;def;odl;idl;res;</Extensions>
    </Filter>
    <Filter Include="Generated Files">
      <UniqueIdentifier>{71ED8ED8-ACB9-4CE9-BBE1-E00B30144E11}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;moc;h;def;odl;idl;res;</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{D9D6E242-F8AF-46E4-B9FD-80ECBC20BA3E}</UniqueIdentifier>
      <Extensions>qrc;*</Extensions>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{D9D6E242-F8AF-46E4-B9FD-80ECBC20BA3E}</UniqueIdentifier>
      <Extensions>qrc;*</Extensions>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Translation Files">
      <UniqueIdentifier>{639EADAA-A684-42e4-A9AD-28FC9BCB8F7C}</UniqueIdentifier>
      <Extensions>ts;xlf</Extensions>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="Translation Files">
      <UniqueIdentifier>{639EADAA-A684-42e4-A9AD-28FC9BCB8F7C}</UniqueIdentifier>
      <Extensions>ts;xlf</Extensions>
      <ParseFiles>false</ParseFiles>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Utils\AccessibilityProxy.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomPropertyEditorDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomPropertyOptionsDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomPropertyWidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Utils\DoubleSpinBox.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Enum.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Utils\InplaceEditing.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Install.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MultiProperty.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Utils\MultilineTextDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Property.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PropertyBase.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Core\PropertyBool.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GUI\PropertyButton.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PropertyConnector.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\PropertyDelegate.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\PropertyDelegateAux.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\Core\PropertyDelegateBool.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\GUI\PropertyDelegateButton.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\Core\PropertyDelegateDouble.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\Core\PropertyDelegateEnum.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\Core\PropertyDelegateEnumFlags.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\PropertyDelegateFactory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\Core\PropertyDelegateFloat.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\Utils\PropertyDelegateGeoCoord.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\Utils\PropertyDelegateGeoPoint.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Auxiliary\PropertyDelegateInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\Core\PropertyDelegateInt.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PropertyDelegateMetaEnum.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\Utils\PropertyDelegateMisc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\Utils\PropertyDelegatePropertySet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\GUI\PropertyDelegateQBrush.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\GUI\PropertyDelegateQColor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\GUI\PropertyDelegateQFont.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\GUI\PropertyDelegateQPen.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\Core\PropertyDelegateQPoint.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\Core\PropertyDelegateQPointF.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\Core\PropertyDelegateQRect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\Core\PropertyDelegateQRectF.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\Core\PropertyDelegateQSize.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\Core\PropertyDelegateQSizeF.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\Core\PropertyDelegateQString.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\GUI\PropertyDelegateQVector3D.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\Utils\PropertyDelegateSliderBox.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\Core\PropertyDelegateUInt.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Core\PropertyDouble.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\Utils\PropertyEditorAux.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Delegates\Utils\PropertyEditorHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Core\PropertyEnum.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Core\PropertyEnumFlags.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Core\PropertyFloat.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Core\PropertyInt.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PropertyInt64.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GUI\PropertyQBrush.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GUI\PropertyQColor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GUI\PropertyQFont.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PropertyQKeySequence.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GUI\PropertyQPen.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Core\PropertyQPoint.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Core\PropertyQPointF.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Core\PropertyQRect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Core\PropertyQRectF.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Core\PropertyQSize.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Core\PropertyQSizeF.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Core\PropertyQString.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PropertyQVariant.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GUI\PropertyQVector3D.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PropertySet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Core\PropertyUInt.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PropertyUInt64.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PropertyView.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PropertyWidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PropertyWidgetEx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="QObjectPropertySet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="QObjectPropertyWidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Utils\QtnCompleterItemDelegate.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Utils\QtnCompleterLineEdit.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Utils\QtnConnections.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Utils\QtnInt64SpinBox.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="VarProperty.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="Utils\AccessibilityProxy.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <ClInclude Include="Config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <QtMoc Include="CustomPropertyEditorDialog.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="CustomPropertyOptionsDialog.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="CustomPropertyWidget.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <ClInclude Include="Utils\DoubleSpinBox.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Enum.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FunctionalHelpers.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="IQtnPropertyStateProvider.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Utils\InplaceEditing.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Install.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <QtMoc Include="MultiProperty.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="Utils\MultilineTextDialog.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="Property.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <ClInclude Include="Auxiliary\PropertyAux.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <QtMoc Include="PropertyBase.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="Core\PropertyBool.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="GUI\PropertyButton.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="PropertyConnector.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <ClInclude Include="PropertyCore.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\PropertyDelegate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="PropertyDelegateAttrs.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\PropertyDelegateAux.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\Core\PropertyDelegateBool.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\GUI\PropertyDelegateButton.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\Core\PropertyDelegateDouble.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\Core\PropertyDelegateEnum.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\Core\PropertyDelegateEnumFlags.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\PropertyDelegateFactory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\Core\PropertyDelegateFloat.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\Utils\PropertyDelegateGeoCoord.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\Utils\PropertyDelegateGeoPoint.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Auxiliary\PropertyDelegateInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\Core\PropertyDelegateInt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="PropertyDelegateMetaEnum.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\Utils\PropertyDelegateMisc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\Utils\PropertyDelegatePropertySet.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\GUI\PropertyDelegateQBrush.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\GUI\PropertyDelegateQColor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\GUI\PropertyDelegateQFont.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\GUI\PropertyDelegateQPen.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\Core\PropertyDelegateQPoint.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\Core\PropertyDelegateQPointF.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\Core\PropertyDelegateQRect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\Core\PropertyDelegateQRectF.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\Core\PropertyDelegateQSize.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\Core\PropertyDelegateQSizeF.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\Core\PropertyDelegateQString.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\GUI\PropertyDelegateQVector3D.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\Utils\PropertyDelegateSliderBox.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\Core\PropertyDelegateUInt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <QtMoc Include="Core\PropertyDouble.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <ClInclude Include="Delegates\Utils\PropertyEditorAux.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Delegates\Utils\PropertyEditorHandler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <QtMoc Include="Core\PropertyEnum.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="Core\PropertyEnumFlags.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="Core\PropertyFloat.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <ClInclude Include="PropertyGUI.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <QtMoc Include="Core\PropertyInt.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="PropertyInt64.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <ClInclude Include="Auxiliary\PropertyMacro.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <QtMoc Include="GUI\PropertyQBrush.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="GUI\PropertyQColor.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="GUI\PropertyQFont.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="PropertyQKeySequence.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="GUI\PropertyQPen.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="Core\PropertyQPoint.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="Core\PropertyQPointF.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="Core\PropertyQRect.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="Core\PropertyQRectF.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="Core\PropertyQSize.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="Core\PropertyQSizeF.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="Core\PropertyQString.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="PropertyQVariant.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="GUI\PropertyQVector3D.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="PropertySet.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <ClInclude Include="Auxiliary\PropertyTemplates.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <QtMoc Include="Core\PropertyUInt.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="PropertyUInt64.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="PropertyView.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="PropertyWidget.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="PropertyWidgetEx.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <ClInclude Include="QObjectPropertySet.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <QtMoc Include="QObjectPropertyWidget.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="Utils\QtnCompleterItemDelegate.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="Utils\QtnCompleterLineEdit.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <ClInclude Include="Utils\QtnConnections.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <QtMoc Include="Utils\QtnInt64SpinBox.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <ClInclude Include="StructPropertyBase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <QtMoc Include="VarProperty.h">
      <Filter>Header Files</Filter>
    </QtMoc>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="debug\moc_predefs.h.cbt">
      <Filter>Generated Files</Filter>
    </CustomBuild>
    <CustomBuild Include="release\moc_predefs.h.cbt">
      <Filter>Generated Files</Filter>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include=".\Translations\en.ts">
      <Filter>Translation Files</Filter>
    </None>
    <None Include=".\Translations\ru.ts">
      <Filter>Translation Files</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <QtUic Include="CustomPropertyEditorDialog.ui">
      <Filter>Form Files</Filter>
    </QtUic>
    <QtUic Include="CustomPropertyOptionsDialog.ui">
      <Filter>Form Files</Filter>
    </QtUic>
    <QtUic Include="Utils\MultilineTextDialog.ui">
      <Filter>Form Files</Filter>
    </QtUic>
  </ItemGroup>
  <ItemGroup>
    <QtRcc Include="QtnProperty.qrc">
      <Filter>Resource Files</Filter>
    </QtRcc>
    <None Include="Translations\en.qm">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="Translations\ru.qm">
      <Filter>Resource Files</Filter>
    </None>
  </ItemGroup>
</Project>