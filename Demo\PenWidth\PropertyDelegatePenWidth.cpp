/*******************************************************************************
Copyright (c) 2012-2016 <PERSON> <<EMAIL>>
Copyright (c) 2019 <PERSON> <<EMAIL>>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*******************************************************************************/

#include "PropertyDelegatePenWidth.h"
#include "QtnProperty/Delegates/PropertyDelegateFactory.h"
#include "PropertyPenWidth.h"
#include "QtnProperty/Delegates/Utils/PropertyEditorHandler.h"

#include <QComboBox>
#include <QLineEdit>
#include <QStyledItemDelegate>
#include <QPaintEvent>

void regPenWidthDelegates()
{
	QtnPropertyDelegateFactory::staticInstance().registerDelegateDefault(
		&QtnPropertyPenWidthBase::staticMetaObject,
		&qtnCreateDelegate<QtnPropertyDelegatePenWidth,
			QtnPropertyPenWidthBase>);
}

static void drawPenWidth(
	QStyle *style, PenWidth penWidth, QPainter &painter, QRect rect)
{
	rect.adjust(2, 2, -2, -2);

	QPen pen = painter.pen();

	switch (penWidth)
	{
		case PenWidth::Default:
			qtnDrawValueText("Default", painter, rect, style);
			return;
		case PenWidth::Thin:
			pen.setWidth(1);
			break;
		case PenWidth::Middle:
			pen.setWidth(2);
			break;
		case PenWidth::Thick:
			pen.setWidth(3);
			break;
	}

	painter.save();
	painter.setPen(pen);
	auto midY = rect.center().y();
	painter.drawLine(rect.left(), midY, rect.right(), midY);
	painter.restore();
}

class QtnPropertyPenWidthComboBox : public QComboBox
{
public:
	explicit QtnPropertyPenWidthComboBox(
		QtnPropertyDelegatePenWidth *delegate, QWidget *parent = Q_NULLPTR)
		: QComboBox(parent)
		, m_delegate(delegate)
		, m_property(
			  *static_cast<QtnPropertyPenWidthBase *>(delegate->property()))
		, m_updating(0)
	{
		setLineEdit(nullptr);
		setItemDelegate(new ItemDelegate(this));

		addItem(QString(), QVariant::fromValue(PenWidth::Default));
		addItem(QString(), QVariant::fromValue(PenWidth::Thin));
		addItem(QString(), QVariant::fromValue(PenWidth::Middle));
		addItem(QString(), QVariant::fromValue(PenWidth::Thick));

		updateEditor();

		QObject::connect(&m_property, &QtnPropertyBase::propertyDidChange, this,
			&QtnPropertyPenWidthComboBox::onPropertyDidChange,
			Qt::QueuedConnection);
		QObject::connect(this,
			static_cast<void (QComboBox::*)(int)>(
				&QComboBox::currentIndexChanged),
			this, &QtnPropertyPenWidthComboBox::onCurrentIndexChanged);
	}

protected:
	void paintEvent(QPaintEvent *event) override
	{
		QComboBox::paintEvent(event);

		QPainter painter(this);

		auto index = currentIndex();
		if (index != -1)
		{
			auto penWidth = currentData().value<PenWidth>();
			drawPenWidth(style(), penWidth, painter,
				event->rect().adjusted(0, 0, -event->rect().height(), 0));
		}
	}

	void updateEditor()
	{
		m_updating++;
		setEnabled(m_delegate->stateProperty()->isEditableByUser());
		for (int i = 0; i < count(); ++i)
		{
			if (itemData(i).value<PenWidth>() == m_property.value())
				setCurrentIndex(i);
		}
		m_updating--;
	}

	void onPropertyDidChange(QtnPropertyChangeReason reason)
	{
		if ((reason & QtnPropertyChangeReasonValue))
			updateEditor();
	}

	void onCurrentIndexChanged(int index)
	{
		if (index >= 0)
		{
			m_property.setValue(
				itemData(index).value<PenWidth>(), m_delegate->editReason());
		}
	}

private:
	QtnPropertyDelegatePenWidth *m_delegate;
	QtnPropertyPenWidthBase &m_property;
	unsigned m_updating;

	class ItemDelegate : public QStyledItemDelegate
	{
		QComboBox *m_owner;

	public:
		ItemDelegate(QComboBox *owner)
			: m_owner(owner)
		{
		}

		void paint(QPainter *painter, const QStyleOptionViewItem &option,
			const QModelIndex &index) const override
		{
			QStyledItemDelegate::paint(painter, option, index);
			drawPenWidth(m_owner->style(),
				index.data(Qt::UserRole).value<PenWidth>(), *painter,
				option.rect);
		}
	};
};

bool QtnPropertyDelegatePenWidth::propertyValueToStrImpl(
	QString &strValue) const
{
	return owner().toStr(strValue);
}

void QtnPropertyDelegatePenWidth::drawValueImpl(
	QStylePainter &painter, const QRect &rect) const
{
	drawPenWidth(painter.style(), owner().value(), painter, rect);
}

QWidget *QtnPropertyDelegatePenWidth::createValueEditorImpl(
	QWidget *parent, const QRect &rect, QtnInplaceInfo *inplaceInfo)
{
	auto combo = new QtnPropertyPenWidthComboBox(this, parent);
	combo->setGeometry(rect);
	if (inplaceInfo && stateProperty()->isEditableByUser())
		combo->showPopup();

	return combo;
}
