/****************************************************************************
** Meta object code from reading C++ file 'PropertyWidgetEx.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../PropertyWidgetEx.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'PropertyWidgetEx.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtnPropertyWidgetEx_t {
    QByteArrayData data[6];
    char stringdata0[92];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyWidgetEx_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyWidgetEx_t qt_meta_stringdata_QtnPropertyWidgetEx = {
    {
QT_MOC_LITERAL(0, 0, 19), // "QtnPropertyWidgetEx"
QT_MOC_LITERAL(1, 20, 20), // "deleteActiveProperty"
QT_MOC_LITERAL(2, 41, 0), // ""
QT_MOC_LITERAL(3, 42, 14), // "cutToClipboard"
QT_MOC_LITERAL(4, 57, 15), // "copyToClipboard"
QT_MOC_LITERAL(5, 73, 18) // "pasteFromClipboard"

    },
    "QtnPropertyWidgetEx\0deleteActiveProperty\0"
    "\0cutToClipboard\0copyToClipboard\0"
    "pasteFromClipboard"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyWidgetEx[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       4,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   34,    2, 0x0a /* Public */,
       3,    0,   35,    2, 0x0a /* Public */,
       4,    0,   36,    2, 0x0a /* Public */,
       5,    0,   37,    2, 0x0a /* Public */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void QtnPropertyWidgetEx::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        QtnPropertyWidgetEx *_t = static_cast<QtnPropertyWidgetEx *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->deleteActiveProperty(); break;
        case 1: _t->cutToClipboard(); break;
        case 2: _t->copyToClipboard(); break;
        case 3: _t->pasteFromClipboard(); break;
        default: ;
        }
    }
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyWidgetEx::staticMetaObject = {
    { &QtnPropertyWidget::staticMetaObject, qt_meta_stringdata_QtnPropertyWidgetEx.data,
      qt_meta_data_QtnPropertyWidgetEx,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyWidgetEx::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyWidgetEx::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyWidgetEx.stringdata0))
        return static_cast<void*>(this);
    if (!strcmp(_clname, "QtnPropertyWidgetExDelegate"))
        return static_cast< QtnPropertyWidgetExDelegate*>(this);
    return QtnPropertyWidget::qt_metacast(_clname);
}

int QtnPropertyWidgetEx::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnPropertyWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 4;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
