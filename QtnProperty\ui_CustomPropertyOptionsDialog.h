/********************************************************************************
** Form generated from reading UI file 'CustomPropertyOptionsDialog.ui'
**
** Created by: Qt User Interface Compiler version 5.9.9
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_CUSTOMPROPERTYOPTIONSDIALOG_H
#define UI_CUSTOMPROPERTYOPTIONSDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QDialog>
#include <QtWidgets/QDialogButtonBox>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QRadioButton>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_CustomPropertyOptionsDialog
{
public:
    QVBoxLayout *verticalLayout_2;
    QLabel *label;
    QWidget *widget;
    QHBoxLayout *horizontalLayout;
    QSpinBox *editIndex;
    QLineEdit *editName;
    QGroupBox *typeBox;
    QVBoxLayout *verticalLayout;
    QRadioButton *rbNumeric;
    QRadioButton *rbString;
    QRadioButton *rbBoolean;
    QRadioButton *rbDictionary;
    QRadioButton *rbList;
    QRadioButton *rbNull;
    QDialogButtonBox *buttonBox;

    void setupUi(QDialog *CustomPropertyOptionsDialog)
    {
        if (CustomPropertyOptionsDialog->objectName().isEmpty())
            CustomPropertyOptionsDialog->setObjectName(QStringLiteral("CustomPropertyOptionsDialog"));
        CustomPropertyOptionsDialog->setWindowModality(Qt::NonModal);
        CustomPropertyOptionsDialog->resize(221, 291);
        QSizePolicy sizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(CustomPropertyOptionsDialog->sizePolicy().hasHeightForWidth());
        CustomPropertyOptionsDialog->setSizePolicy(sizePolicy);
        CustomPropertyOptionsDialog->setWindowTitle(QStringLiteral(""));
        CustomPropertyOptionsDialog->setModal(true);
        verticalLayout_2 = new QVBoxLayout(CustomPropertyOptionsDialog);
        verticalLayout_2->setSpacing(9);
        verticalLayout_2->setObjectName(QStringLiteral("verticalLayout_2"));
        verticalLayout_2->setContentsMargins(9, 9, 9, 9);
        label = new QLabel(CustomPropertyOptionsDialog);
        label->setObjectName(QStringLiteral("label"));
        label->setMaximumSize(QSize(16777215, 17));
        label->setMargin(2);

        verticalLayout_2->addWidget(label);

        widget = new QWidget(CustomPropertyOptionsDialog);
        widget->setObjectName(QStringLiteral("widget"));
        widget->setMinimumSize(QSize(0, 21));
        widget->setMaximumSize(QSize(16777215, 21));
        horizontalLayout = new QHBoxLayout(widget);
        horizontalLayout->setObjectName(QStringLiteral("horizontalLayout"));
        horizontalLayout->setContentsMargins(0, 0, 0, 0);
        editIndex = new QSpinBox(widget);
        editIndex->setObjectName(QStringLiteral("editIndex"));

        horizontalLayout->addWidget(editIndex);

        editName = new QLineEdit(widget);
        editName->setObjectName(QStringLiteral("editName"));

        horizontalLayout->addWidget(editName);


        verticalLayout_2->addWidget(widget);

        typeBox = new QGroupBox(CustomPropertyOptionsDialog);
        typeBox->setObjectName(QStringLiteral("typeBox"));
        typeBox->setMinimumSize(QSize(0, 141));
        verticalLayout = new QVBoxLayout(typeBox);
        verticalLayout->setObjectName(QStringLiteral("verticalLayout"));
        rbNumeric = new QRadioButton(typeBox);
        rbNumeric->setObjectName(QStringLiteral("rbNumeric"));
        rbNumeric->setChecked(false);

        verticalLayout->addWidget(rbNumeric);

        rbString = new QRadioButton(typeBox);
        rbString->setObjectName(QStringLiteral("rbString"));
        rbString->setChecked(false);

        verticalLayout->addWidget(rbString);

        rbBoolean = new QRadioButton(typeBox);
        rbBoolean->setObjectName(QStringLiteral("rbBoolean"));

        verticalLayout->addWidget(rbBoolean);

        rbDictionary = new QRadioButton(typeBox);
        rbDictionary->setObjectName(QStringLiteral("rbDictionary"));

        verticalLayout->addWidget(rbDictionary);

        rbList = new QRadioButton(typeBox);
        rbList->setObjectName(QStringLiteral("rbList"));

        verticalLayout->addWidget(rbList);

        rbNull = new QRadioButton(typeBox);
        rbNull->setObjectName(QStringLiteral("rbNull"));
        rbNull->setChecked(true);

        verticalLayout->addWidget(rbNull);


        verticalLayout_2->addWidget(typeBox);

        buttonBox = new QDialogButtonBox(CustomPropertyOptionsDialog);
        buttonBox->setObjectName(QStringLiteral("buttonBox"));
        buttonBox->setStandardButtons(QDialogButtonBox::Cancel|QDialogButtonBox::Ok);
        buttonBox->setCenterButtons(true);

        verticalLayout_2->addWidget(buttonBox);


        retranslateUi(CustomPropertyOptionsDialog);

        QMetaObject::connectSlotsByName(CustomPropertyOptionsDialog);
    } // setupUi

    void retranslateUi(QDialog *CustomPropertyOptionsDialog)
    {
        label->setText(QApplication::translate("CustomPropertyOptionsDialog", "Name:", Q_NULLPTR));
        typeBox->setTitle(QApplication::translate("CustomPropertyOptionsDialog", "Type", Q_NULLPTR));
        rbNumeric->setText(QApplication::translate("CustomPropertyOptionsDialog", "Numeric", Q_NULLPTR));
        rbString->setText(QApplication::translate("CustomPropertyOptionsDialog", "String", Q_NULLPTR));
        rbBoolean->setText(QApplication::translate("CustomPropertyOptionsDialog", "Boolean", Q_NULLPTR));
        rbDictionary->setText(QApplication::translate("CustomPropertyOptionsDialog", "Dictionary", Q_NULLPTR));
        rbList->setText(QApplication::translate("CustomPropertyOptionsDialog", "List", Q_NULLPTR));
        rbNull->setText(QApplication::translate("CustomPropertyOptionsDialog", "Null", Q_NULLPTR));
        Q_UNUSED(CustomPropertyOptionsDialog);
    } // retranslateUi

};

namespace Ui {
    class CustomPropertyOptionsDialog: public Ui_CustomPropertyOptionsDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_CUSTOMPROPERTYOPTIONSDIALOG_H
