/****************************************************************************
** Meta object code from reading C++ file 'PropertyBase.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../PropertyBase.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'PropertyBase.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtnPropertyBase_t {
    QByteArrayData data[17];
    char stringdata0[193];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyBase_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyBase_t qt_meta_stringdata_QtnPropertyBase = {
    {
QT_MOC_LITERAL(0, 0, 15), // "QtnPropertyBase"
QT_MOC_LITERAL(1, 16, 18), // "propertyWillChange"
QT_MOC_LITERAL(2, 35, 0), // ""
QT_MOC_LITERAL(3, 36, 23), // "QtnPropertyChangeReason"
QT_MOC_LITERAL(4, 60, 6), // "reason"
QT_MOC_LITERAL(5, 67, 19), // "QtnPropertyValuePtr"
QT_MOC_LITERAL(6, 87, 8), // "newValue"
QT_MOC_LITERAL(7, 96, 6), // "typeId"
QT_MOC_LITERAL(8, 103, 17), // "propertyDidChange"
QT_MOC_LITERAL(9, 121, 4), // "name"
QT_MOC_LITERAL(10, 126, 11), // "displayName"
QT_MOC_LITERAL(11, 138, 11), // "description"
QT_MOC_LITERAL(12, 150, 2), // "id"
QT_MOC_LITERAL(13, 153, 10), // "isEditable"
QT_MOC_LITERAL(14, 164, 16), // "isEditableByUser"
QT_MOC_LITERAL(15, 181, 5), // "state"
QT_MOC_LITERAL(16, 187, 5) // "value"

    },
    "QtnPropertyBase\0propertyWillChange\0\0"
    "QtnPropertyChangeReason\0reason\0"
    "QtnPropertyValuePtr\0newValue\0typeId\0"
    "propertyDidChange\0name\0displayName\0"
    "description\0id\0isEditable\0isEditableByUser\0"
    "state\0value"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyBase[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       2,   14, // methods
       8,   34, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    3,   24,    2, 0x06 /* Public */,
       8,    1,   31,    2, 0x06 /* Public */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3, 0x80000000 | 5, QMetaType::Int,    4,    6,    7,
    QMetaType::Void, 0x80000000 | 3,    4,

 // properties: name, type, flags
       9, QMetaType::QString, 0x00095001,
      10, QMetaType::QString, 0x00095001,
      11, QMetaType::QString, 0x00095001,
      12, QMetaType::Int, 0x00095001,
      13, QMetaType::Bool, 0x00095001,
      14, QMetaType::Bool, 0x00095001,
      15, QMetaType::UInt, 0x00095001,
      16, QMetaType::QVariant, 0x00095003,

       0        // eod
};

void QtnPropertyBase::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        QtnPropertyBase *_t = static_cast<QtnPropertyBase *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->propertyWillChange((*reinterpret_cast< QtnPropertyChangeReason(*)>(_a[1])),(*reinterpret_cast< QtnPropertyValuePtr(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3]))); break;
        case 1: _t->propertyDidChange((*reinterpret_cast< QtnPropertyChangeReason(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 0:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyChangeReason >(); break;
            case 1:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyValuePtr >(); break;
            }
            break;
        case 1:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyChangeReason >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            typedef void (QtnPropertyBase::*_t)(QtnPropertyChangeReason , QtnPropertyValuePtr , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtnPropertyBase::propertyWillChange)) {
                *result = 0;
                return;
            }
        }
        {
            typedef void (QtnPropertyBase::*_t)(QtnPropertyChangeReason );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtnPropertyBase::propertyDidChange)) {
                *result = 1;
                return;
            }
        }
    }
#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty) {
        QtnPropertyBase *_t = static_cast<QtnPropertyBase *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QString*>(_v) = _t->name(); break;
        case 1: *reinterpret_cast< QString*>(_v) = _t->displayName(); break;
        case 2: *reinterpret_cast< QString*>(_v) = _t->description(); break;
        case 3: *reinterpret_cast< qint32*>(_v) = _t->id(); break;
        case 4: *reinterpret_cast< bool*>(_v) = _t->isWritable(); break;
        case 5: *reinterpret_cast< bool*>(_v) = _t->isEditableByUser(); break;
        case 6: *reinterpret_cast< quint32*>(_v) = _t->state(); break;
        case 7: *reinterpret_cast< QVariant*>(_v) = _t->valueAsVariant(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        QtnPropertyBase *_t = static_cast<QtnPropertyBase *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 7: _t->fromVariant(*reinterpret_cast< QVariant*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
}

const QMetaObject QtnPropertyBase::staticMetaObject = {
    { &QObject::staticMetaObject, qt_meta_stringdata_QtnPropertyBase.data,
      qt_meta_data_QtnPropertyBase,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyBase::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyBase::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyBase.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int QtnPropertyBase::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 2)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 2;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 2)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 2;
    }
#ifndef QT_NO_PROPERTIES
   else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 8;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}

// SIGNAL 0
void QtnPropertyBase::propertyWillChange(QtnPropertyChangeReason _t1, QtnPropertyValuePtr _t2, int _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)), const_cast<void*>(reinterpret_cast<const void*>(&_t3)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void QtnPropertyBase::propertyDidChange(QtnPropertyChangeReason _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
