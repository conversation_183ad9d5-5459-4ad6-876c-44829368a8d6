/*******************************************************************************
Copyright (c) 2012-2016 <PERSON> <<EMAIL>>
Copyright (c) 2015-2019 <PERSON> <<EMAIL>>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*******************************************************************************/

#ifndef PROPERTYCORE_H
#define PROPERTYCORE_H

#include "PropertySet.h"
#include "Core/PropertyBool.h"
#include "Core/PropertyInt.h"
#include "Core/PropertyUInt.h"
#include "Core/PropertyFloat.h"
#include "Core/PropertyDouble.h"
#include "Core/PropertyEnum.h"
#include "Core/PropertyEnumFlags.h"
#include "Core/PropertyQString.h"
#include "Core/PropertyQRect.h"
#include "Core/PropertyQRectF.h"
#include "Core/PropertyQPoint.h"
#include "Core/PropertyQPointF.h"
#include "Core/PropertyQSize.h"
#include "Core/PropertyQSizeF.h"

#endif // PROPERTYCORE_H
