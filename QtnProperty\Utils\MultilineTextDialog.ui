<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MultilineTextDialog</class>
 <widget class="QDialog" name="MultilineTextDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string/>
  </property>
  <property name="sizeGripEnabled">
   <bool>true</bool>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="leftMargin">
    <number>6</number>
   </property>
   <property name="topMargin">
    <number>6</number>
   </property>
   <property name="rightMargin">
    <number>6</number>
   </property>
   <property name="bottomMargin">
    <number>6</number>
   </property>
   <item>
    <widget class="QPlainTextEdit" name="plainTextEdit">
     <property name="tabChangesFocus">
      <bool>true</bool>
     </property>
     <property name="lineWrapMode">
      <enum>QPlainTextEdit::WidgetWidth</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
     <property name="centerButtons">
      <bool>false</bool>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
