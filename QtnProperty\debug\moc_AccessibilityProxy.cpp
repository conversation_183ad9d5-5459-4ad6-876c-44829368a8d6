/****************************************************************************
** Meta object code from reading C++ file 'AccessibilityProxy.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../Utils/AccessibilityProxy.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'AccessibilityProxy.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtnAccessibilityProxy_t {
    QByteArrayData data[19];
    char stringdata0[273];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnAccessibilityProxy_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnAccessibilityProxy_t qt_meta_stringdata_QtnAccessibilityProxy = {
    {
QT_MOC_LITERAL(0, 0, 21), // "QtnAccessibilityProxy"
QT_MOC_LITERAL(1, 22, 5), // "owner"
QT_MOC_LITERAL(2, 28, 16), // "QtnPropertyView*"
QT_MOC_LITERAL(3, 45, 0), // ""
QT_MOC_LITERAL(4, 46, 14), // "activeProperty"
QT_MOC_LITERAL(5, 61, 16), // "QtnPropertyBase*"
QT_MOC_LITERAL(6, 78, 11), // "propertySet"
QT_MOC_LITERAL(7, 90, 15), // "QtnPropertySet*"
QT_MOC_LITERAL(8, 106, 12), // "findProperty"
QT_MOC_LITERAL(9, 119, 10), // "nameOrPath"
QT_MOC_LITERAL(10, 130, 18), // "propertyUnderPoint"
QT_MOC_LITERAL(11, 149, 5), // "point"
QT_MOC_LITERAL(12, 155, 21), // "ensureVisibleProperty"
QT_MOC_LITERAL(13, 177, 8), // "property"
QT_MOC_LITERAL(14, 186, 16), // "propertyNameRect"
QT_MOC_LITERAL(15, 203, 17), // "propertyValueRect"
QT_MOC_LITERAL(16, 221, 18), // "propertyActionRect"
QT_MOC_LITERAL(17, 240, 11), // "actionIndex"
QT_MOC_LITERAL(18, 252, 20) // "propertyDelegateName"

    },
    "QtnAccessibilityProxy\0owner\0"
    "QtnPropertyView*\0\0activeProperty\0"
    "QtnPropertyBase*\0propertySet\0"
    "QtnPropertySet*\0findProperty\0nameOrPath\0"
    "propertyUnderPoint\0point\0ensureVisibleProperty\0"
    "property\0propertyNameRect\0propertyValueRect\0"
    "propertyActionRect\0actionIndex\0"
    "propertyDelegateName"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnAccessibilityProxy[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
      10,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   64,    3, 0x0a /* Public */,
       4,    0,   65,    3, 0x0a /* Public */,
       6,    0,   66,    3, 0x0a /* Public */,
       8,    1,   67,    3, 0x0a /* Public */,
      10,    1,   70,    3, 0x0a /* Public */,
      12,    1,   73,    3, 0x0a /* Public */,
      14,    1,   76,    3, 0x0a /* Public */,
      15,    1,   79,    3, 0x0a /* Public */,
      16,    2,   82,    3, 0x0a /* Public */,
      18,    1,   87,    3, 0x0a /* Public */,

 // slots: parameters
    0x80000000 | 2,
    0x80000000 | 5,
    0x80000000 | 7,
    0x80000000 | 5, QMetaType::QString,    9,
    0x80000000 | 5, QMetaType::QPoint,   11,
    QMetaType::Void, 0x80000000 | 5,   13,
    QMetaType::QRect, 0x80000000 | 5,   13,
    QMetaType::QRect, 0x80000000 | 5,   13,
    QMetaType::QRect, 0x80000000 | 5, QMetaType::Int,   13,   17,
    QMetaType::QString, 0x80000000 | 5,   13,

       0        // eod
};

void QtnAccessibilityProxy::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        QtnAccessibilityProxy *_t = static_cast<QtnAccessibilityProxy *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: { QtnPropertyView* _r = _t->owner();
            if (_a[0]) *reinterpret_cast< QtnPropertyView**>(_a[0]) = std::move(_r); }  break;
        case 1: { QtnPropertyBase* _r = _t->activeProperty();
            if (_a[0]) *reinterpret_cast< QtnPropertyBase**>(_a[0]) = std::move(_r); }  break;
        case 2: { QtnPropertySet* _r = _t->propertySet();
            if (_a[0]) *reinterpret_cast< QtnPropertySet**>(_a[0]) = std::move(_r); }  break;
        case 3: { QtnPropertyBase* _r = _t->findProperty((*reinterpret_cast< QString(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QtnPropertyBase**>(_a[0]) = std::move(_r); }  break;
        case 4: { QtnPropertyBase* _r = _t->propertyUnderPoint((*reinterpret_cast< QPoint(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QtnPropertyBase**>(_a[0]) = std::move(_r); }  break;
        case 5: _t->ensureVisibleProperty((*reinterpret_cast< QtnPropertyBase*(*)>(_a[1]))); break;
        case 6: { QRect _r = _t->propertyNameRect((*reinterpret_cast< QtnPropertyBase*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QRect*>(_a[0]) = std::move(_r); }  break;
        case 7: { QRect _r = _t->propertyValueRect((*reinterpret_cast< QtnPropertyBase*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QRect*>(_a[0]) = std::move(_r); }  break;
        case 8: { QRect _r = _t->propertyActionRect((*reinterpret_cast< QtnPropertyBase*(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])));
            if (_a[0]) *reinterpret_cast< QRect*>(_a[0]) = std::move(_r); }  break;
        case 9: { QString _r = _t->propertyDelegateName((*reinterpret_cast< QtnPropertyBase*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 5:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyBase* >(); break;
            }
            break;
        case 6:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyBase* >(); break;
            }
            break;
        case 7:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyBase* >(); break;
            }
            break;
        case 8:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyBase* >(); break;
            }
            break;
        case 9:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyBase* >(); break;
            }
            break;
        }
    }
}

const QMetaObject QtnAccessibilityProxy::staticMetaObject = {
    { &QObject::staticMetaObject, qt_meta_stringdata_QtnAccessibilityProxy.data,
      qt_meta_data_QtnAccessibilityProxy,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnAccessibilityProxy::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnAccessibilityProxy::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnAccessibilityProxy.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int QtnAccessibilityProxy::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 10)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 10)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
