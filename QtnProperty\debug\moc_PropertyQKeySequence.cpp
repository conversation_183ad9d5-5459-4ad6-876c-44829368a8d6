/****************************************************************************
** Meta object code from reading C++ file 'PropertyQKeySequence.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../PropertyQKeySequence.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'PropertyQKeySequence.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtnPropertyQKeySequenceBase_t {
    QByteArrayData data[1];
    char stringdata0[28];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQKeySequenceBase_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQKeySequenceBase_t qt_meta_stringdata_QtnPropertyQKeySequenceBase = {
    {
QT_MOC_LITERAL(0, 0, 27) // "QtnPropertyQKeySequenceBase"

    },
    "QtnPropertyQKeySequenceBase"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQKeySequenceBase[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyQKeySequenceBase::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyQKeySequenceBase::staticMetaObject = {
    { &QtnSinglePropertyBase<QKeySequence>::staticMetaObject, qt_meta_stringdata_QtnPropertyQKeySequenceBase.data,
      qt_meta_data_QtnPropertyQKeySequenceBase,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQKeySequenceBase::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQKeySequenceBase::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQKeySequenceBase.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyBase<QKeySequence>::qt_metacast(_clname);
}

int QtnPropertyQKeySequenceBase::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyBase<QKeySequence>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyQKeySequenceCallback_t {
    QByteArrayData data[1];
    char stringdata0[32];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQKeySequenceCallback_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQKeySequenceCallback_t qt_meta_stringdata_QtnPropertyQKeySequenceCallback = {
    {
QT_MOC_LITERAL(0, 0, 31) // "QtnPropertyQKeySequenceCallback"

    },
    "QtnPropertyQKeySequenceCallback"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQKeySequenceCallback[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyQKeySequenceCallback::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyQKeySequenceCallback::staticMetaObject = {
    { &QtnSinglePropertyCallback<QtnPropertyQKeySequenceBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyQKeySequenceCallback.data,
      qt_meta_data_QtnPropertyQKeySequenceCallback,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQKeySequenceCallback::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQKeySequenceCallback::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQKeySequenceCallback.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyCallback<QtnPropertyQKeySequenceBase>::qt_metacast(_clname);
}

int QtnPropertyQKeySequenceCallback::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyCallback<QtnPropertyQKeySequenceBase>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyQKeySequence_t {
    QByteArrayData data[1];
    char stringdata0[24];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQKeySequence_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQKeySequence_t qt_meta_stringdata_QtnPropertyQKeySequence = {
    {
QT_MOC_LITERAL(0, 0, 23) // "QtnPropertyQKeySequence"

    },
    "QtnPropertyQKeySequence"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQKeySequence[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyQKeySequence::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyQKeySequence::staticMetaObject = {
    { &QtnSinglePropertyValue<QtnPropertyQKeySequenceBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyQKeySequence.data,
      qt_meta_data_QtnPropertyQKeySequence,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQKeySequence::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQKeySequence::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQKeySequence.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyValue<QtnPropertyQKeySequenceBase>::qt_metacast(_clname);
}

int QtnPropertyQKeySequence::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyValue<QtnPropertyQKeySequenceBase>::qt_metacall(_c, _id, _a);
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
