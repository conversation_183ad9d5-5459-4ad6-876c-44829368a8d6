/****************************************************************************
** Meta object code from reading C++ file 'PropertyInt64.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../PropertyInt64.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'PropertyInt64.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtnPropertyInt64Base_t {
    QByteArrayData data[1];
    char stringdata0[21];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyInt64Base_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyInt64Base_t qt_meta_stringdata_QtnPropertyInt64Base = {
    {
QT_MOC_LITERAL(0, 0, 20) // "QtnPropertyInt64Base"

    },
    "QtnPropertyInt64Base"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyInt64Base[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyInt64Base::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyInt64Base::staticMetaObject = {
    { &QtnNumericPropertyBase<QtnSinglePropertyBase<qint64>>::staticMetaObject, qt_meta_stringdata_QtnPropertyInt64Base.data,
      qt_meta_data_QtnPropertyInt64Base,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyInt64Base::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyInt64Base::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyInt64Base.stringdata0))
        return static_cast<void*>(this);
    return QtnNumericPropertyBase<QtnSinglePropertyBase<qint64>>::qt_metacast(_clname);
}

int QtnPropertyInt64Base::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnNumericPropertyBase<QtnSinglePropertyBase<qint64>>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyInt64_t {
    QByteArrayData data[1];
    char stringdata0[17];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyInt64_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyInt64_t qt_meta_stringdata_QtnPropertyInt64 = {
    {
QT_MOC_LITERAL(0, 0, 16) // "QtnPropertyInt64"

    },
    "QtnPropertyInt64"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyInt64[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyInt64::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyInt64::staticMetaObject = {
    { &QtnNumericPropertyValue<QtnPropertyInt64Base>::staticMetaObject, qt_meta_stringdata_QtnPropertyInt64.data,
      qt_meta_data_QtnPropertyInt64,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyInt64::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyInt64::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyInt64.stringdata0))
        return static_cast<void*>(this);
    return QtnNumericPropertyValue<QtnPropertyInt64Base>::qt_metacast(_clname);
}

int QtnPropertyInt64::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnNumericPropertyValue<QtnPropertyInt64Base>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyInt64Callback_t {
    QByteArrayData data[1];
    char stringdata0[25];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyInt64Callback_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyInt64Callback_t qt_meta_stringdata_QtnPropertyInt64Callback = {
    {
QT_MOC_LITERAL(0, 0, 24) // "QtnPropertyInt64Callback"

    },
    "QtnPropertyInt64Callback"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyInt64Callback[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyInt64Callback::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyInt64Callback::staticMetaObject = {
    { &QtnSinglePropertyCallback<QtnPropertyInt64Base>::staticMetaObject, qt_meta_stringdata_QtnPropertyInt64Callback.data,
      qt_meta_data_QtnPropertyInt64Callback,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyInt64Callback::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyInt64Callback::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyInt64Callback.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyCallback<QtnPropertyInt64Base>::qt_metacast(_clname);
}

int QtnPropertyInt64Callback::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyCallback<QtnPropertyInt64Base>::qt_metacall(_c, _id, _a);
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
