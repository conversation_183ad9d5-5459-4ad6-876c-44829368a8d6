<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="enabled">
   <bool>true</bool>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>675</width>
    <height>468</height>
   </rect>
  </property>
  <property name="font">
   <font>
    <pointsize>10</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>PropertyWidget Demo</string>
  </property>
  <property name="iconSize">
   <size>
    <width>24</width>
    <height>24</height>
   </size>
  </property>
  <widget class="QWidget" name="centralWidget">
   <property name="sizePolicy">
    <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout">
    <item>
     <widget class="QtnPropertyWidget" name="pw" native="true">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>150</width>
        <height>0</height>
       </size>
      </property>
     </widget>
    </item>
    <item>
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <widget class="QPushButton" name="editButton">
        <property name="text">
         <string>Edit in modal dialog...</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="label">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Script examples:
samplePS.IntProperty.value = 15;
print(samplePS.SubPropertySet.SwitchProperty.state);
samplePS.UIntProperty.propertyDidChange.connect(
    function(p1, p2) {
        print(p2.name+&quot; is &quot;+p2.value+&quot; now&quot;);
});</string>
        </property>
        <property name="textFormat">
         <enum>Qt::PlainText</enum>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="dbgButton">
        <property name="enabled">
         <bool>true</bool>
        </property>
        <property name="text">
         <string>Show script debugger</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="label_2">
        <property name="text">
         <string>String examples:
EnumProperty = blue
EnableSubPropertySet = true
SubPropertySet.FolderNameProperty = &quot;/home/<USER>/string>
        </property>
        <property name="textFormat">
         <enum>Qt::PlainText</enum>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPlainTextEdit" name="plainTextEdit"/>
      </item>
      <item>
       <widget class="QPushButton" name="pushButton">
        <property name="text">
         <string>from string</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButton_3">
        <property name="text">
         <string>to string</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButton_2">
        <property name="text">
         <string>edit QObject properties</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="verticalSpacer">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>QtnPropertyWidget</class>
   <extends>QWidget</extends>
   <header>QtnProperty/PropertyWidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
