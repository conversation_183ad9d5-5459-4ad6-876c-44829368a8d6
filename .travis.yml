language: cpp
dist: trusty

compiler:
  - gcc

branches:
  only:
    - master
    - develop

os:
  - linux

before_install:
  - sudo add-apt-repository ppa:beineri/opt-qt593-trusty -y
  - sudo add-apt-repository ppa:ubuntu-toolchain-r/test -y
  - sudo apt-get update -q

install:
  - sudo apt-get install flex bison
  - sudo apt-get install qt59base qt59script qt59tools
  - source /opt/qt59/bin/qt59-env.sh
  - sudo apt-get install -qq g++-4.8
  - sudo update-alternatives --install /usr/bin/g++ g++ /usr/bin/g++-4.8 90

script:
    - qmake
    - make
    - ./bin-linux-gcc-x86_64/QtnPropertyTests
