<FlowDocument xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" FontFamily="Segoe UI" FontSize="12">
  <FlowDocument.Tag><![CDATA[{
  "timestamp": "2025-08-07 15:43:34",
  "files": {
    "G:\\Code\\Qt\\QtnProperty\\Demo\\QtnPropertyDemo.vcxproj": {
      "before": "7V17U+O6Fe/fnel38KR770CncSAEdiEhdyAJ3MzltSQsbSczjGOLxHf9Wltmob39Rv2QPZIs2Y6cJ8Esu95lklgP63V8fud3JEvn//vTnxq/PNqW8oD8wHSdw9K2ulVSkKO7humMDkshvi9/KP3S/MufG1e++zvSsdJG91po4b7mjxAODkvHoWkZJQXu4sDVGGPvoFIJ9DGytUC1Td13A/ceq7prVwz0gCzXQ37FDoYkW6W6tbVTgrsrSqOLkX3qu6GnnGlDZB2WogJbrnNvjkJfw1DBgCWG5FmxStfRrdBAh6VrZCEtQH887tV4DsiTulUzStOopINJZci/xpWl4XvXt5twk0ZFXLF4CGD9kZV5TuXaaBiOZlaNplh7xRoV0cesy6EJMBj4KdXtp5Y71Cypo09D02j+p9pqt/b2q0flam2rVt7ZqbbKH6o77fLezkmt+uGk1X5/3Pqv6BuaJ+qva9fFF5qNAk/TUfMjdnjhbWS7jUo6OsrzG3r66voGpK596j2AaDYqPIikoOXEDYjEyPZcHyvR6ByW3m18akWyeqXh8ebgXIhky/PUSJpVD7oiKCmVrI6BITZMIn2HpZ81zw3q7zZSo775x7sNLh6bLMXhIftOyCELKHHpTt2By6iQub7rWgHCzYft2nYsezw06p7LEHshbps+PJau/9RU1cHQdMpfTWenWraDB317v1YrP37Yu9urDRqVyeTRXY76Z+emY9rmv1HQug6dvmmjM3Poa/7TTaCNUPMepAE1KnPTRfdrjTVf0zHye1D/CxfDF0hyMpAnTD7Y/ScPNY88zzJ1+qBPCD+NjvJ1Hbi5jQxTwyhuvM+eeGhndnyU+co3bWgZ6wtZDNPRJE+mkHHZZQ/OM+RDqIJXlg6DqKTvXkZ4K3OQkO5yaiitfljmlFbuPGLkEIiGxwkDNMe6Kpn4GZI4XVNxYe+NEQC+UFVyC28C5BM9Dg/lpJpNKEg1hGRc3yYqjB7NAAcb7EF4t7H0zVjGTaHDK4mOSULDiz2z300/8YakBJCMx7lGrDnRw6l0tItBXYJmhfFXEgPLRxSCYkNuKcxcANmgTF4DeLrXX4MUvCQKY6YFMWzW3GTJQopLEg0dOa6PmJhHiL3mSmA/BOTvyuXwKpyZzmcwun1kIwdr1pqLjyyPiUJ42c+XtEn0nS1nHD1YubwWz5c2qRa8IHZrXlA8/s/pZamw/OVMqsJsKcs0wAiRAdMd7FZilz9bpcvgx3u9ZbVc2zMtFAWAhjsyGBvQrIhugjqgFriJgqZaBysc/hLdWlfr7QMIgL9ddV/dH7BPYqBXt7Z3iXFuMt66aDq4061pEPq9RA4gY0ukPro4Pesskb6n+6aHl85AGc0SuVqg8BboT/tz4CE9GMRUqP7Txqxx2wR+M2tYGXtIDf6lRx0SzfK/9AP/QbNC1NICrJBL07FMB9GfAfZNHffgE2w2GoLHvgv1Gl2gr/TaR/fIB48LOjaBZjojpfx1p7a9tcW+P+zDd622v79Hv2u7/Pt9dP1BSbYsqlSqObyicRuCANlDC/lnYHFBiWcu41zNGOSOpiURNzmGVgSo6xAPCWVsnCfKESIPffITWU5oXiCJDqDMlMg4sxloUOkeDKx5b+q3mu+QPm3Wdt+/r9dqe+9hiNvZaaA7psTET3XnUUd0RH/VHAOGb9TsPTl6oyKHixpdDolD7ASUA8H/RO9NRMQZoADg2ay7zrVHaAsygJgng0XiKx95vqujIHD9WNUFzbvb7kX78rZXv7noti7bnfod/wERO9U6fO7V6net6/5dr9O6ue7cXVze3R5dX3QvTnv1i3bn+Oa0/rFPQsXv2277tNPv3Z11j0nU6U2X/+y1rrtX/fRV//LyTKRtXUIJJF905582plQcBmFKTDwIcYK+S/qVy5QUnugld+RrdlvD2jE4/MRgUNTIjBFZwdcBw8F9Hc1zcGqa/bGPNAMZ7bMz8EmlE4iMvdDzfBiXHtZ8HHrHmuMgv8lQLDtOZO3D7fEt8Ybc4aOAuE5x16HODZZ9ery4RST4Z8SX2qSfO41KKpAlBQ/KBHY1iCEVd3as8NrIQ44BOogg2Dyssszh4Au2NdNR4Wc9GCPL2qnS3wnQo9eL3ApAMcKyZbIAmC2TnKHT8jkoPC2TjeATTZ9UysnuTWnmZETWuERGfdK+WKBL662DAfh1nSCwBmSE4PIGm1YwsJ/ugi8W/0pFgVcTj0CmIdobkU8S+27jY7/dvd6kF6cHg5ZrIGbHCOftdIdjCnDlhqT6QY7O6g2OYz9/CV1cr5wfXXRPOr1+u3PVAd1z0frnAQYnYuR4pcjPmKfigHaOgmPn7y2gLSCYCuad7TplIKfYB59rlIXPh7DLPXWL/I/ivHAIXkpwRPfdz8iJbry3u7873Ktt12q6fr9t3EdpLc0ZheBEjVL9jd+CK/YjXx+bGFAk9CfSsFYuD+5EE3YekR4S6wT05gMQM/gVqadpsUK7nCLQZcSxShyCCajmmnhqvLhDFlucbeHT2ZZJkjeT+zE/Azi/KUi822CUbBOEU0gmmVZQ0SMYFhDJU4pKXgPIu8TlbRDUAE1I9HnUR9lxImsvHPaeAuAfzUiEGpU4KJFqNYxoVBJKunFuGlb8KERzFQQ9KGTcgENw5BALYjJGVKPjEJup4/uu3xoj/XMQGVtyuMiSghKYcUlds1SNSqJijWsUuKGvI4koxaj9NiyY1U0XsBUyOoHNt8W2G6WpdDjpZOdExFpZfTROkxZAisAU7JWxbtAaBXv9DtirEc1h5cpdwQfOWcYQkOQ7oLG8GxcisRGtzpnErpW3rq71QZvGELcoYZ0glWnWSSS4oJ5T3aRASiLqaVCStT7uye63ALkCsNgly1qWIYUMXVbIQunnUvkI/2QZfngCOqCKrKChsF5J+X5oKGOSL8ZCE1yxYJU/Eqtcq0kROdZXtyxW4pMxvQTDRDC/eE3sObhsmctC1T2Pr+LITnt0POCrO46OW67l+kvlacNivBF4kRbOe+KjL6JEnpsEzi8WZsmlnBBG5rbmZz7TnpAvZaeh8zNfIehPA4+l/Dxi/i1S7V6svekqr1rVxatIPf1zBcZ+gvWYljtKNllab5xefkwkLwywa9MV5JlyOuarzSBtPF3RdcCfFzzHX5Ix3594OsZ129XvYIrHQPeBOp47PQ2LbgeQg3gbU+YGq2fK082CYpce8T6DK3LNbZlnxyYqrCjldjR/CL/4VCL8pJOJ7HuvRqKyJxQhhk38wY/EpCK7SkwrsoBoYpFdxFOLyWsxucgC+fSiUi7rbCGGX763tAfXPyQLKCA4WjuhqJXUqCnlbvug8hHDH11vUWGfiVUXlWiVQIXOFdAF05Dp9ADeBzAQyym8yeBhtd2psRDB3PXQ8ZFDQCGe6SBebK58MoMQFkj1cGiY7qC6Va0OOmQhs+ebARp8ag2osT8478HP7Zpaq6k7u9Wt93xpSDQd8IIFkfXdJ60XKO8ofDQtExZdDz71ptwepqhS/aZswKr1zUHkYFd+M3Ew2N7imeEnTMdU92CRhLo1CHUfT/TOgrfLvt8gtNd6uwDc9chY6y1BYNfcZtDvq970otM/+Uev/dugpn4QPSr6MKlWlbKrkGc0EQblkpX+VAnGWvEcJmNg0mzNWvH8sqUkih7DLAYrJy6Ymf/rhha5zQATUVGADtGvuBbrRDo6j0feNWIzj6VmsgfeFM5JLckX5ShBYJhUwJqYRi9grYA1XBf2z/OAd1UEAstAAE/KNBh8d7AmKcF8QE0qdilIA4SPeV4Ec9nUL8PtkCMFzCr9TUFkBq3NFyQLKihUYUEFMzhuQQV/YCqYpV45JcxwNufKC4+OyX4OdC8B7rPOlx9Olg6WyyuSxKyhelNIKBks+eJgQRbJoqjCByqcuwXwFcBHdtoR4JI78EkakTDH/GBPKp4wyOVAL5tG8pdw4/nDBHjxmWTR7alZ72gBdJwzNRvL89JZ2fHsjLPmoOdkTU/n8kLZtO6crHwOd/r8c+oGMQef0uKopS/jiM7gpqnuZoW/KSMjo0n5mhkF3S7o9qwp5cLq+IGtjgztOml0UJ2b9xQsKVSQbab1c52J5UYHb/wr0mxSBQHerCveFABKRl2+8Few7IJlp5ZQFXhX4B1z4jJlmi/eSdqQMGxSkVzQTio9SbBpd8x2KWez6yzOmGasnKnmxhozi39TsFnwRrpLd7Fil22NvvIaqmLFLlsBXKzYXW4F1uwVu5n6dRJJmdbPmzrSUgWasjq8zjStaP8rskdaB0Efo954U0Ao2SwFf4T9QYs3Vea8ElPgXoF7q7zy8+3jnqQQCYWkmj0f1JPKT5LIRTBvcRYpTV7yADXHFb+8TIGiPADe+iRNL94B3anCOy90S1n4Lt4BLd4BXc3lXHhmf2DPLFeqGVp2klfypGQbgVwXAPOCBc7yAPV1CKYofrbXds37Ikj4z6uRMXRvCiClhhVMs2Ca8zdfKJhmwTTXzzRnKNX88VDSjIRy8hrmhoZSLZLEk9eGGAUz/K2Lc0+xWVGOVDMu800BZzFXWcxVrmMbowJJCyRdP5LGSpUDpwjJm0CKgvPli6LY2dj40jxRVONt+U0ls6OghQUtLGihvPPelP11xDZvfF/C520A9GNvlRfr0PzATFKAhP3FFXmZFzOkQgnZE4XOBjKZ5C2wyyw/By9+75GeDuWh1Ha1ZD9aOSWp23KpEzvsQVtSL45GRyTF9UjSXP6S6lKZ+MrexYsR07iLZ0kS8Pm5kiOZTJ21lpi0X0yyqvoQJzb8JTuo0nOu2q4ekvOlGxURJE6rWudeiStwXX7Q7D0c6whHuAUDA45fGxihbT9F3orJCr7CdsC6pZSPH9kOpOJMP7IH8Bdb+4zItsV8d1IHdvoewRwnnGH7VaeHVCrlkx47pnatR9x2lHmW1pyeVarNi5szpZkSoNxmjPj5A3Q7UyG/L6MtM6Qy1egZHrGXtvqfOYZQ87yfDgl6imfjWdvsSv2Z55MhFb7ocyEbERT7pf0gQjO5V3FqMwV52wlILJBPJF3AOMmCxYQJEZrfGCSm6rbQ3vihyfbGT5zOKeaT2dm/sNwqf12QoVjnKTQCmlFrkltJhyax2CekJTcwuumm9rUOzdxwaKLFr4hE341QSjrt2xdJqco5CaRU7uLimA0AWZpYqPRvTg8narYmLdx5fIt6ONEPkRYWIeCKz+lsASLyoth8NbAolmDoa50qICoRmm9bGCWlsoz+TfRCTqIoVTcXQZRKTdm+M8RQ1rsrGsiRR+4+YRtPUsk1nx7CS/w2DNUppxTBPnTIdzSroqoVsFXLsKP+TpWebLS9X6uV4VCdu70aHG3kXHVOid9H4a36+a+PW1tb7Tr7Pqqb9woih8Zb6AFZyrYycrGrfGp1HGMyZW4qlu/b03FCWxkhB/kadn3Rgr8r0CblV6QZyA9EaG6mcNJFXBcXr6mS+dC+gsBK+uFZ4mqQU5HfgtBKzf6GRFaqm5BRMt8iLmYJ7LqV9yjPN71SLZyEilfwyS9iVoA7iyppePOqOKKPAxns+DhFm5AhnhpbbBRZbBQ59ZigH3vdQawa+boDEQLYkJt9Zz/dEf/9mFpwudltpMhkc2eQlxefTQP9pZI1Ca/hDJftg4PBRwx/9NjUAftMHJ46WO8ZssVWjqu9V1scGhv3W3FoLDsld8pavTd0up5QyOP8EElSgDngkVzm4mi0CBuzPdfHCrDQ35GOD0vvNj61+po/Qji40vB4Mz6dWm15nopZFF+y1ejS7Ke+G3qwB8kQWYelziNGTmC6TnQblrZBXhMjRTT/Dw==",
      "after": "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"
    },
    "G:\\Code\\Qt\\QtnProperty\\Demo\\QtnPropertyDemo.vcxproj.filters": {
      "before": "5Vnfb6JAEO7zJfd8yT0Z31dQROGgNixCr0kvaXK53MslDYVVtmFZyo+e5tL//VYtLBTPUpUm7Rl9cIT5dubb+WZYP30+OdHPFiTo3KM4wTQ87fZ7YreDQpd6OJyfdrN0BpTu2eTjB/0qprfITTvs8jA57fppGn0RhMT1EXGSHsFuTBM6S3suJYKH7lFAIxQLJLnJcOAJA1GUusxNp6NfpIicxzSL1l+ZwcZBiuLORegGmYdOuzaNSYcZUbK5o8Ne+o8Q32XowkNhimcYxZM/qioNVUVUgSxDAwxvVA9Ae6wCxZ5CKA6UkQWlB12o3bhaxNqltUhRuAo7mWRYF0pfi0uunDhB66VMZk6QIF0oWTZX6cJm/f9HNOcoRLGTIq8BQeO+NVXYGxgmVMHQtFQAodUHlihCSewPh1a/35QgN4o0V3MXC41QV/M1D8006gUaZp8YJdoW+p4j5h2F8hU5HqugBjUjqaosKSJQVDhlNSMOgaJYEIwGsg0teWBBGzalxNd8RorPKPGJhkPGRehqi8Tbg4q3vv7vNItd1CD/Q9scS2O7D8yxIYOhNB4BQxyNwMCQBtOxPDAGtt00/7wkysXgR7fajZNqTkJWn8UebLyvaFjfYo0oXVphRnb2E0ucKqY6koFpyjbjxhsDYzqSgG1YtgihKdqK/AAiNN/VU0rtYd+G8eYWTJbXTJSv/bUIHSfFK4ctp/kNrFoXqrNSfXQyA5OSiI1KfHr65uDwJw49+rvHFKJEx+OYUq7upy1SKPzl00xh4AAG/JVvUQOaNKBx+0BTFKA5GzvaBbRjdFfElkOujC0FeBGmNThmu8QJG6Nb4e7SWaK4hrm2toR4hdhu9FK/Bpr/0BJuhcsWOaxm9FUz2XIGCROSlsghSw87AZ0f7L6BQmZJSglcPYRyCStppF9XyPI0WldI7q/QSG7iEFtU8hhQweNT8lagXLJylXwxYO69iCw3cLhKXeWA6/pqA22XQraBV63mPLxNVbeBl5fwv9XxcNRtu7PC4n7scbfFbuEmvl+qGd0zk9zxTqxaNnND78VZbIhYCNmhAA2ErD7qTRGhPfZAsl1Gn5xv1JUsd1jkNDdw9lZz/euglCR56+hzrGgaja7HBltX2KuEtSmwV4EqiqtVtKLCjoTCC7u6y6MYsWOUpOf33Ju0PhM8uyO4X15OtV6d4evSPt+iGc/C5A15N0iRtIMhGugSj5xntBRlhuvZ5Cf8dVXi7ooYuYkjFCEe6H/PAB9FcVaPLReY1XnTs9Edhj7f1tcq5xovXAD7e2PzL8/kLw==",
      "after": "5Vjfb5swEO7zpD3uPcq7AwkhgUFTYQJdpVZqNVV7mVRRcIorjCmYLtHUv31zSPkVsiRNx6R2UfKQC7nPd5/vu7N/fTo60k/mJOg8ojjBNDzu9ntit4NCl3o4vDvupmwGlO7J5OMH/TKm98hlHf54mBx3fcaiz4KQuD4iTtIj2I1pQmes51IieOgRBTRCsUCS2xQHnjAQRanL3XQ6+hlD5DSmaZR95QYbBwzFnbPQDVIPHXdtGpMON6Jk9Y8Of+nXIX5I0ZmHQoZnGMWTn6oqDVVFVIEsQwMMb1UPQHusAsWeQigOlJEFpSddaPxxuYjMpTVnKFyGnUxSrAuVr8Ujl06coGwpk5kTJEgXKpbVU7qwWv//Ec0pClHsMOTtQdC4b00V/gaGCVUwNC0VQGj1gSWKUBL7w6HV7+9LkBtFmqu587lGqKv5modmGvUCDfNPjBJtA327iHlHoXxBjscraI+akVRVlhQRKCqc8poRh0BRLAhGA9mGljywoA33pcTXfE6KzynxiYZDzkXoavPEO4CKt77+rzSNXbRH/oe2OZbGdh+YY0MGQ2k8AoY4GoGBIQ2mY3lgDGx73/yXJVEtBj+6124dpjkJWX7mB7DxvqLhfYs3IrawwpRs7SeWOFVMdSQD05Rtzo03BsZ0JAHbsGwRQlO0FfkJROhuW0+ptIdDG8abWzBZ3HBRvvEzEfo7KV46bDnNb2DVulCflZqjkxmYlER8VCqnpwsHh99w6NEfPa4QFTqex5Rqda+3SKHwl08zhaEEMOD3fIsa0KQBjdsHmqIA3fGxo11AO0YPRWw55NLYUoBnIWvAcds5TvgY3Qp3584CxQ3MzNoS4iXiu9FjfgM0/6El3BqXLXJYz+g/zWTLGSRcSFoihyw87AT07tXudyvkFbugbileFXX0m9pYnUMb2ph5ynVxze0GTXyVezN4Pg2XK69A5NKUq+FLoQrveTSFoYSr1U8OmNVRG2jblLANvHrV5uGtqrcNvLxU/6yCr0Zd25E1/g7ibdt+r+fvsLxt89/IV27ovTRP21AKGTrc6W4B2jBBTRGhPX6Q2Cx/a/cSDR0qHBbVmyaMEri8ZyvrdzmRRzHiR8Ok5/fcW9ZUu51Apd8ManesV+wabxbbFDfxy3u+RoyZozy+Na8Fawf73B2IWYZepvSZtlkzknweXp4xG7FUfO2dxlrPKrbLhm1aO8U0oMvNXw+ZX2OubnMnvwE="
    }
  }
}]]></FlowDocument.Tag>
  <Section Margin="0,24" TextAlignment="Center">
    <Paragraph FontSize="24" FontWeight="Bold" Margin="12,0">
      <LineBreak />
      <Span Foreground="Gray">Qt Visual Studio Tools</Span>
    </Paragraph>
    <Paragraph FontSize="42" Margin="12,0" FontWeight="Bold">
      <Span TextDecorations="Underline">Project Format Conversion</Span>
    </Paragraph>
    <Paragraph Margin="12,8" FontSize="18">
      <Span>Report generated on 2025-08-07 15:43:34</Span>
    </Paragraph>
  </Section>
  <Section>
    <Paragraph FontSize="32" FontWeight="Bold" Margin="12,0">
      <Span>Files</Span>
    </Paragraph>
    <Paragraph Margin="24,12,0,0">
      <Span FontFamily="Consolas" FontSize="14" Background="WhiteSmoke"><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj]]></Span>
      <LineBreak />
      <Hyperlink NavigateUri="file://G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj?before">[Before]</Hyperlink>
      <Hyperlink NavigateUri="file://G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj?after">[After]</Hyperlink>
      <Hyperlink NavigateUri="file://G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj?diff&amp;before&amp;after">
                        [Diff before/after]
                    </Hyperlink>
      <Hyperlink NavigateUri="file://G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj?diff&amp;before&amp;current">
                        [Diff before/current]
                    </Hyperlink>
      <Hyperlink NavigateUri="file://G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj?diff&amp;after&amp;current">
                        [Diff after/current]
                    </Hyperlink>
      <LineBreak />
    </Paragraph>
    <Paragraph Margin="24,12,0,0">
      <Span FontFamily="Consolas" FontSize="14" Background="WhiteSmoke"><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj.filters]]></Span>
      <LineBreak />
      <Hyperlink NavigateUri="file://G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj.filters?before">[Before]</Hyperlink>
      <Hyperlink NavigateUri="file://G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj.filters?after">[After]</Hyperlink>
      <Hyperlink NavigateUri="file://G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj.filters?diff&amp;before&amp;after">
                        [Diff before/after]
                    </Hyperlink>
      <Hyperlink NavigateUri="file://G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj.filters?diff&amp;before&amp;current">
                        [Diff before/current]
                    </Hyperlink>
      <Hyperlink NavigateUri="file://G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj.filters?diff&amp;after&amp;current">
                        [Diff after/current]
                    </Hyperlink>
      <LineBreak />
    </Paragraph>
  </Section>
  <Section>
    <Paragraph FontSize="32" FontWeight="Bold" Margin="12,0">
      <Span>Changes</Span>
    </Paragraph>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Replacing paths with "$(QTDIR)"]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;..\..\QtnProperty;.;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include]]></Span><Span><![CDATA[;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtWidgets]]></Span><Span><![CDATA[;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtGui]]></Span><Span><![CDATA[;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtANGLE]]></Span><Span><![CDATA[;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtScript]]></Span><Span><![CDATA[;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtScriptTools]]></Span><Span><![CDATA[;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtCore]]></Span><Span><![CDATA[;.;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\mkspecs\win32-msvc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;..\..\QtnProperty;.;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include]]></Span><Span><![CDATA[;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include\QtWidgets]]></Span><Span><![CDATA[;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include\QtGui]]></Span><Span><![CDATA[;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include\QtANGLE]]></Span><Span><![CDATA[;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include\QtScript]]></Span><Span><![CDATA[;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include\QtScriptTools]]></Span><Span><![CDATA[;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include\QtCore]]></Span><Span><![CDATA[;.;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\mkspecs\win32-msvc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalDependencies>D:\Qt\Qt5.9.9\5]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[9.9\msvc2015_64\lib\qtmain.]]></Span><Span><![CDATA[lib;shell32.lib;QtnProperty.lib;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[9\5.9.9\msvc2015_64\lib\Qt5Widgets.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\lib\Qt5Gui]]></Span><Span><![CDATA[.lib;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[9\msvc2015_64\lib\Qt5Script.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[9.9\5.9.9\msvc2015_64\lib\Qt5ScriptTools.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\lib\Qt5Core]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalDependencies>$(QTDIR)\lib\qtmain]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[lib;shell32.lib;QtnProperty.lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt5Widgets]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt5Gui]]></Span><Span><![CDATA[.lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt5Script]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt5ScriptTools]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt5Core]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalLibraryDirectories>D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\lib]]></Span><Span><![CDATA[;C:\openssl\lib;C:\Utils\my_sql\my_sql\lib;C:\Utils\postgresql\pgsql\lib;$(QTDIR)\lib;G:\Code\Qt\QtnProperty\bin-win32-msvc1944-x86_64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalLibraryDirectories>$(QTDIR)\lib]]></Span><Span><![CDATA[;C:\openssl\lib;C:\Utils\my_sql\my_sql\lib;C:\Utils\postgresql\pgsql\lib;$(QTDIR)\lib;G:\Code\Qt\QtnProperty\bin-win32-msvc1944-x86_64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;..\..\QtnProperty;.;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include]]></Span><Span><![CDATA[;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtWidgets]]></Span><Span><![CDATA[;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtGui]]></Span><Span><![CDATA[;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtANGLE]]></Span><Span><![CDATA[;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtScript]]></Span><Span><![CDATA[;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtScriptTools]]></Span><Span><![CDATA[;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\include\QtCore]]></Span><Span><![CDATA[;.;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\mkspecs\win32-msvc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;..\..\QtnProperty;.;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include]]></Span><Span><![CDATA[;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include\QtWidgets]]></Span><Span><![CDATA[;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include\QtGui]]></Span><Span><![CDATA[;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include\QtANGLE]]></Span><Span><![CDATA[;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include\QtScript]]></Span><Span><![CDATA[;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include\QtScriptTools]]></Span><Span><![CDATA[;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include\QtCore]]></Span><Span><![CDATA[;.;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\mkspecs\win32-msvc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalDependencies>D:\Qt\Qt5.9.9\5]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[9.9\msvc2015_64\lib\qtmaind.]]></Span><Span><![CDATA[lib;shell32.lib;QtnProperty.lib;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[9\5.9.9\msvc2015_64\lib\Qt5Widgetsd.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\lib\Qt5Guid]]></Span><Span><![CDATA[.lib;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[9\msvc2015_64\lib\Qt5Scriptd.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[9.9\5.9.9\msvc2015_64\lib\Qt5ScriptToolsd.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\lib\Qt5Cored]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalDependencies>$(QTDIR)\lib\qtmaind]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[lib;shell32.lib;QtnProperty.lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt5Widgetsd]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt5Guid]]></Span><Span><![CDATA[.lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt5Scriptd]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt5ScriptToolsd]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt5Cored]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalLibraryDirectories>D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\lib]]></Span><Span><![CDATA[;C:\openssl\lib;C:\Utils\my_sql\my_sql\lib;C:\Utils\postgresql\pgsql\lib;$(QTDIR)\lib;G:\Code\Qt\QtnProperty\bin-win32-msvc1944-x86_64\debug;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalLibraryDirectories>$(QTDIR)\lib]]></Span><Span><![CDATA[;C:\openssl\lib;C:\Utils\my_sql\my_sql\lib;C:\Utils\postgresql\pgsql\lib;$(QTDIR)\lib;G:\Code\Qt\QtnProperty\bin-win32-msvc1944-x86_64\debug;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MainWindow.h;moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MainWindow.h;moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.9.9/5.9.9/msvc2015_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" MainWindow.h -o moc_MainWindow.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" MainWindow.h -o moc_MainWindow.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MainWindow.h;moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MainWindow.h;moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.9.9/5.9.9/msvc2015_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" MainWindow.h -o moc_MainWindow.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" MainWindow.h -o moc_MainWindow.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">AB\PropertyABColor.h;moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">AB\PropertyABColor.h;moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.9.9/5.9.9/msvc2015_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" AB\PropertyABColor.h -o moc_PropertyABColor.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" AB\PropertyABColor.h -o moc_PropertyABColor.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">AB\PropertyABColor.h;moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">AB\PropertyABColor.h;moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.9.9/5.9.9/msvc2015_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" AB\PropertyABColor.h -o moc_PropertyABColor.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" AB\PropertyABColor.h -o moc_PropertyABColor.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Freq\PropertyFreq.h;moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Freq\PropertyFreq.h;moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.9.9/5.9.9/msvc2015_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Freq\PropertyFreq.h -o moc_PropertyFreq.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Freq\PropertyFreq.h -o moc_PropertyFreq.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Freq\PropertyFreq.h;moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Freq\PropertyFreq.h;moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.9.9/5.9.9/msvc2015_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Freq\PropertyFreq.h -o moc_PropertyFreq.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Freq\PropertyFreq.h -o moc_PropertyFreq.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Layer\PropertyLayer.h;moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Layer\PropertyLayer.h;moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.9.9/5.9.9/msvc2015_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Layer\PropertyLayer.h -o moc_PropertyLayer.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Layer\PropertyLayer.h -o moc_PropertyLayer.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Layer\PropertyLayer.h;moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Layer\PropertyLayer.h;moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.9.9/5.9.9/msvc2015_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Layer\PropertyLayer.h -o moc_PropertyLayer.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Layer\PropertyLayer.h -o moc_PropertyLayer.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">PenWidth\PropertyPenWidth.h;moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">PenWidth\PropertyPenWidth.h;moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.9.9/5.9.9/msvc2015_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" PenWidth\PropertyPenWidth.h -o moc_PropertyPenWidth.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" PenWidth\PropertyPenWidth.h -o moc_PropertyPenWidth.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">PenWidth\PropertyPenWidth.h;moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">PenWidth\PropertyPenWidth.h;moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.9.9/5.9.9/msvc2015_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" PenWidth\PropertyPenWidth.h -o moc_PropertyPenWidth.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" PenWidth\PropertyPenWidth.h -o moc_PropertyPenWidth.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">mydialog.h;moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">mydialog.h;moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.9.9/5.9.9/msvc2015_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" mydialog.h -o moc_mydialog.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" mydialog.h -o moc_mydialog.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">mydialog.h;moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">mydialog.h;moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.9.9/5.9.9/msvc2015_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" mydialog.h -o moc_mydialog.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" mydialog.h -o moc_mydialog.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -E ]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp 2&gt;NUL &gt;moc_predefs.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -E ]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp 2&gt;NUL &gt;moc_predefs.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -E ]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp 2&gt;NUL &gt;moc_predefs.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -E ]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp 2&gt;NUL &gt;moc_predefs.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MainWindow.ui;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\uic]]></Span><Span><![CDATA[.exe;QtnProperty\PropertyWidget.h;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MainWindow.ui;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\uic]]></Span><Span><![CDATA[.exe;QtnProperty\PropertyWidget.h;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\uic]]></Span><Span><![CDATA[.exe MainWindow.ui -o ui_MainWindow.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\uic]]></Span><Span><![CDATA[.exe MainWindow.ui -o ui_MainWindow.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MainWindow.ui;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\uic]]></Span><Span><![CDATA[.exe;QtnProperty\PropertyWidget.h;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MainWindow.ui;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\uic]]></Span><Span><![CDATA[.exe;QtnProperty\PropertyWidget.h;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\uic]]></Span><Span><![CDATA[.exe MainWindow.ui -o ui_MainWindow.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\uic]]></Span><Span><![CDATA[.exe MainWindow.ui -o ui_MainWindow.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">mydialog.ui;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\uic]]></Span><Span><![CDATA[.exe;QtnProperty\PropertyWidgetEx.h;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">mydialog.ui;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\uic]]></Span><Span><![CDATA[.exe;QtnProperty\PropertyWidgetEx.h;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\uic]]></Span><Span><![CDATA[.exe mydialog.ui -o ui_mydialog.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\uic]]></Span><Span><![CDATA[.exe mydialog.ui -o ui_mydialog.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">mydialog.ui;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\uic]]></Span><Span><![CDATA[.exe;QtnProperty\PropertyWidgetEx.h;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">mydialog.ui;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\uic]]></Span><Span><![CDATA[.exe;QtnProperty\PropertyWidgetEx.h;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\uic]]></Span><Span><![CDATA[.exe mydialog.ui -o ui_mydialog.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\uic]]></Span><Span><![CDATA[.exe mydialog.ui -o ui_mydialog.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.9.9/5.9.9/msvc2015_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Demo.peg.h -o moc_Demo.peg.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Demo.peg.h -o moc_Demo.peg.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">D:\Qt\Qt5.9.9\5.9.9\msvc2015_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.9.9/5.9.9/msvc2015_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Demo.peg.h -o moc_Demo.peg.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -IG:/Code/Qt/QtnProperty/Demo -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Demo.peg.h -o moc_Demo.peg.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Replacing paths with "."]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-IG:/Code/Qt/QtnProperty/Demo ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" MainWindow.h -o moc_MainWindow.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" MainWindow.h -o moc_MainWindow.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-IG:/Code/Qt/QtnProperty/Demo ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" MainWindow.h -o moc_MainWindow.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" MainWindow.h -o moc_MainWindow.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-IG:/Code/Qt/QtnProperty/Demo ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" AB\PropertyABColor.h -o moc_PropertyABColor.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" AB\PropertyABColor.h -o moc_PropertyABColor.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-IG:/Code/Qt/QtnProperty/Demo ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" AB\PropertyABColor.h -o moc_PropertyABColor.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" AB\PropertyABColor.h -o moc_PropertyABColor.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-IG:/Code/Qt/QtnProperty/Demo ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Freq\PropertyFreq.h -o moc_PropertyFreq.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Freq\PropertyFreq.h -o moc_PropertyFreq.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-IG:/Code/Qt/QtnProperty/Demo ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Freq\PropertyFreq.h -o moc_PropertyFreq.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Freq\PropertyFreq.h -o moc_PropertyFreq.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-IG:/Code/Qt/QtnProperty/Demo ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Layer\PropertyLayer.h -o moc_PropertyLayer.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Layer\PropertyLayer.h -o moc_PropertyLayer.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-IG:/Code/Qt/QtnProperty/Demo ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Layer\PropertyLayer.h -o moc_PropertyLayer.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Layer\PropertyLayer.h -o moc_PropertyLayer.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-IG:/Code/Qt/QtnProperty/Demo ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" PenWidth\PropertyPenWidth.h -o moc_PropertyPenWidth.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" PenWidth\PropertyPenWidth.h -o moc_PropertyPenWidth.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-IG:/Code/Qt/QtnProperty/Demo ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" PenWidth\PropertyPenWidth.h -o moc_PropertyPenWidth.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" PenWidth\PropertyPenWidth.h -o moc_PropertyPenWidth.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-IG:/Code/Qt/QtnProperty/Demo ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" mydialog.h -o moc_mydialog.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" mydialog.h -o moc_mydialog.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-IG:/Code/Qt/QtnProperty/Demo ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" mydialog.h -o moc_mydialog.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" mydialog.h -o moc_mydialog.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-IG:/Code/Qt/QtnProperty/Demo ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Demo.peg.h -o moc_Demo.peg.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Demo.peg.h -o moc_Demo.peg.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-IG:/Code/Qt/QtnProperty/Demo ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Demo.peg.h -o moc_Demo.peg.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Demo.peg.h -o moc_Demo.peg.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Converting custom build steps to Qt/MSBuild items]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalIncludeDirectories>.;..]]></Span><Span><![CDATA[\..\QtnProperty]]></Span><Span Background="LightCoral"><![CDATA[;.;]]></Span><Span><![CDATA[$(QTDIR)\include;$(QTDIR)\include\QtWidgets;$(QTDIR)\include\QtGui;$(QTDIR)\include\QtANGLE;$(QTDIR)\include\QtScript;$(QTDIR)\include\QtScriptTools;$(QTDIR)\include\QtCore]]></Span><Span Background="LightCoral"><![CDATA[;.;]]></Span><Span><![CDATA[$(QTDIR)\mkspecs\win32-msvc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;..]]></Span><Span><![CDATA[\..\QtnProperty]]></Span><Span Background="LightGreen"><![CDATA[;]]></Span><Span><![CDATA[$(QTDIR)\include;$(QTDIR)\include\QtWidgets;$(QTDIR)\include\QtGui;$(QTDIR)\include\QtANGLE;$(QTDIR)\include\QtScript;$(QTDIR)\include\QtScriptTools;$(QTDIR)\include\QtCore]]></Span><Span Background="LightGreen"><![CDATA[;]]></Span><Span><![CDATA[$(QTDIR)\mkspecs\win32-msvc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <OutputFile>$(ProjectDir)\moc_%(Filename).cpp</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <Define>UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;NDEBUG;QT_NO_DEBUG;QT_WIDGETS_LIB;QT_GUI_LIB;QT_SCRIPT_LIB;QT_SCRIPTTOOLS_LIB;QT_CORE_LIB</Define>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <CompilerFlavor>msvc</CompilerFlavor>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <Include>./moc_predefs.h</Include>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <ExecutionDescription>Moc'ing %(Identity)...</ExecutionDescription>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <DynamicSource>output</DynamicSource>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <IncludePath>GeneratedFiles\$(ConfigurationName);GeneratedFiles;$(QTDIR)/mkspecs/win32-msvc;.;G:/Code/Qt/QtnProperty;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include;C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um</IncludePath>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    </QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtUic>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <OutputFile>$(ProjectDir)\ui_%(Filename).h</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <ExecutionDescription>Uic'ing %(Identity)...</ExecutionDescription>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    </QtUic>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalIncludeDirectories>.;..]]></Span><Span><![CDATA[\..\QtnProperty]]></Span><Span Background="LightCoral"><![CDATA[;.;]]></Span><Span><![CDATA[$(QTDIR)\include;$(QTDIR)\include\QtWidgets;$(QTDIR)\include\QtGui;$(QTDIR)\include\QtANGLE;$(QTDIR)\include\QtScript;$(QTDIR)\include\QtScriptTools;$(QTDIR)\include\QtCore]]></Span><Span Background="LightCoral"><![CDATA[;.;]]></Span><Span><![CDATA[$(QTDIR)\mkspecs\win32-msvc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;..]]></Span><Span><![CDATA[\..\QtnProperty]]></Span><Span Background="LightGreen"><![CDATA[;]]></Span><Span><![CDATA[$(QTDIR)\include;$(QTDIR)\include\QtWidgets;$(QTDIR)\include\QtGui;$(QTDIR)\include\QtANGLE;$(QTDIR)\include\QtScript;$(QTDIR)\include\QtScriptTools;$(QTDIR)\include\QtCore]]></Span><Span Background="LightGreen"><![CDATA[;]]></Span><Span><![CDATA[$(QTDIR)\mkspecs\win32-msvc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtUic>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <ExecutionDescription>Uic'ing %(Identity)...</ExecutionDescription>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <OutputFile>$(ProjectDir)\ui_%(Filename).h</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    </QtUic>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <OutputFile>$(ProjectDir)\moc_%(Filename).cpp</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <Define>UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;DEBUG;QT_WIDGETS_LIB;QT_GUI_LIB;QT_SCRIPT_LIB;QT_SCRIPTTOOLS_LIB;QT_CORE_LIB</Define>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <CompilerFlavor>msvc</CompilerFlavor>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <Include>./moc_predefs.h</Include>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <ExecutionDescription>Moc'ing %(Identity)...</ExecutionDescription>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <DynamicSource>output</DynamicSource>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <IncludePath>GeneratedFiles\$(ConfigurationName);GeneratedFiles;$(QTDIR)/mkspecs/win32-msvc;.;G:/Code/Qt/QtnProperty;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include;C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um</IncludePath>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    </QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="MainWindow.]]></Span><Span Background="LightCoral"><![CDATA[h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="MainWindow.]]></Span><Span Background="LightGreen"><![CDATA[h" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalInputs]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MainWindow]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[h;moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[Include="AB\PropertyABColor]]></Span><Span><![CDATA[.]]></Span><Span Background="LightGreen"><![CDATA[h" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" MainWindow.h -o moc_MainWindow.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MOC MainWindow.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">moc_MainWindow.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MainWindow.h;moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" MainWindow.h -o moc_MainWindow.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MOC MainWindow.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">moc_MainWindow.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <CustomBuild Include="AB\PropertyABColor.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">AB\PropertyABColor.h;moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" AB\PropertyABColor.h -o moc_PropertyABColor.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MOC AB/PropertyABColor.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">moc_PropertyABColor.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">AB\PropertyABColor.h;moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" AB\PropertyABColor.h -o moc_PropertyABColor.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MOC AB/PropertyABColor.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">moc_PropertyABColor.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="Freq\PropertyFreq.]]></Span><Span Background="LightCoral"><![CDATA[h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="Freq\PropertyFreq.]]></Span><Span Background="LightGreen"><![CDATA[h" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalInputs]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Freq\PropertyFreq]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[h;moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[Include="Layer\PropertyLayer]]></Span><Span><![CDATA[.]]></Span><Span Background="LightGreen"><![CDATA[h" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<Command]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Freq\PropertyFreq.h -o moc_PropertyFreq.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[Include="PenWidth\PropertyPenWidth]]></Span><Span><![CDATA[.]]></Span><Span Background="LightGreen"><![CDATA[h"]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[/>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<Message]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MOC]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[Freq/PropertyFreq.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[Include="mydialog.h"]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[/>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">moc_PropertyFreq.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Freq\PropertyFreq.h;moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Freq\PropertyFreq.h -o moc_PropertyFreq.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MOC Freq/PropertyFreq.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">moc_PropertyFreq.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <CustomBuild Include="Layer\PropertyLayer.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Layer\PropertyLayer.h;moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Layer\PropertyLayer.h -o moc_PropertyLayer.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MOC Layer/PropertyLayer.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">moc_PropertyLayer.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Layer\PropertyLayer.h;moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Layer\PropertyLayer.h -o moc_PropertyLayer.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MOC Layer/PropertyLayer.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">moc_PropertyLayer.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <CustomBuild Include="PenWidth\PropertyPenWidth.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">PenWidth\PropertyPenWidth.h;moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" PenWidth\PropertyPenWidth.h -o moc_PropertyPenWidth.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MOC PenWidth/PropertyPenWidth.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">moc_PropertyPenWidth.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">PenWidth\PropertyPenWidth.h;moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" PenWidth\PropertyPenWidth.h -o moc_PropertyPenWidth.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MOC PenWidth/PropertyPenWidth.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">moc_PropertyPenWidth.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <CustomBuild Include="mydialog.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">mydialog.h;moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" mydialog.h -o moc_mydialog.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MOC mydialog.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">moc_mydialog.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">mydialog.h;moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" mydialog.h -o moc_mydialog.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MOC mydialog.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">moc_mydialog.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="moc_Demo.peg.cpp" />]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="moc_MainWindow.cpp" />]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="moc_PropertyABColor.cpp" />]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="moc_PropertyFreq.cpp" />]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="moc_PropertyLayer.cpp" />]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="moc_PropertyPenWidth.cpp" />]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="moc_mydialog.cpp" />]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClInclude Include="ui_MainWindow.h" />]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClInclude Include="ui_mydialog.h" />]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="MainWindow.]]></Span><Span Background="LightCoral"><![CDATA[ui">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtUic]]></Span><Span><![CDATA[ Include="MainWindow.]]></Span><Span Background="LightGreen"><![CDATA[ui" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<FileType>Document</FileType>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtUic Include="mydialog.ui" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MainWindow.ui;$(QTDIR)\bin\uic.exe;QtnProperty\PropertyWidget.h;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\uic.exe MainWindow.ui -o ui_MainWindow.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">UIC MainWindow.ui</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ui_MainWindow.h;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MainWindow.ui;$(QTDIR)\bin\uic.exe;QtnProperty\PropertyWidget.h;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\uic.exe MainWindow.ui -o ui_MainWindow.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">UIC MainWindow.ui</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ui_MainWindow.h;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <CustomBuild Include="mydialog.ui">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <FileType>Document</FileType>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">mydialog.ui;$(QTDIR)\bin\uic.exe;QtnProperty\PropertyWidgetEx.h;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\uic.exe mydialog.ui -o ui_mydialog.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">UIC mydialog.ui</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ui_mydialog.h;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">mydialog.ui;$(QTDIR)\bin\uic.exe;QtnProperty\PropertyWidgetEx.h;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\uic.exe mydialog.ui -o ui_mydialog.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">UIC mydialog.ui</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ui_mydialog.h;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="Demo.peg.]]></Span><Span Background="LightCoral"><![CDATA[h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="Demo.peg.]]></Span><Span Background="LightGreen"><![CDATA[h" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Demo.peg.h;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DNDEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Demo.peg.h -o moc_Demo.peg.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">my_moc_header</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">moc_Demo.peg.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Demo.peg.h;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -DWIN64 -D_CRT_SECURE_NO_WARNINGS -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SCRIPT_LIB -DQT_SCRIPTTOOLS_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IG:/Code/Qt/QtnProperty -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" Demo.peg.h -o moc_Demo.peg.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">my_moc_header</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">moc_Demo.peg.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj.filters]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="MainWindow.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="MainWindow.h">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="AB\PropertyABColor.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="AB\PropertyABColor.h">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="Freq\PropertyFreq.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="Freq\PropertyFreq.h">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="Layer\PropertyLayer.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="Layer\PropertyLayer.h">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="PenWidth\PropertyPenWidth.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="PenWidth\PropertyPenWidth.h">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="mydialog.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="mydialog.h">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="moc_Demo.peg.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="moc_MainWindow.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="moc_PropertyABColor.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="moc_PropertyFreq.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="moc_PropertyLayer.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="moc_PropertyPenWidth.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="moc_mydialog.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClInclude Include="ui_MainWindow.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClInclude>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClInclude Include="ui_mydialog.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClInclude>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="MainWindow.ui">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtUic]]></Span><Span><![CDATA[ Include="MainWindow.ui">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtUic>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="mydialog.ui">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtUic]]></Span><Span><![CDATA[ Include="mydialog.ui">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtUic>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="Demo.peg.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="Demo.peg.h">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Enabling multi-processor compilation]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <MultiProcessorCompilation>true</MultiProcessorCompilation>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <MultiProcessorCompilation>true</MultiProcessorCompilation>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Setting default Windows SDK]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Project format version]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<Keyword>Qt4VSv1.0</Keyword>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<Keyword>QtVS_v304</Keyword>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Fallback for QTMSBUILD environment variable]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtMsBuild Condition="'$(QtMsBuild)'=='' OR !Exists('$(QtMsBuild)\qt.targets')">$(MSBuildProjectDirectory)\QtMsBuild</QtMsBuild>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Default Qt properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <Import Project="$(QtMsBuild)\qt_defaults.props" Condition="Exists('$(QtMsBuild)\qt_defaults.props')" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Qt build settings]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Release|x64'" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Warn if Qt/MSBuild is not found]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <Target Name="QtMsBuildNotFound" BeforeTargets="CustomBuild;ClCompile" Condition="!Exists('$(QtMsBuild)\qt.targets') OR !Exists('$(QtMsBuild)\Qt.props')">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <Message Importance="High" Text="QtMsBuild: could not locate qt.targets, qt.props; project may not build correctly." />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  </Target>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Qt property sheet]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <Import Project="$(QtMsBuild)\Qt.props" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <Import Project="$(QtMsBuild)\Qt.props" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Qt targets]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <Import Project="$(QtMsBuild)\qt.targets" Condition="Exists('$(QtMsBuild)\qt.targets')" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Copying Qt build reference to QtInstall project property]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'" />]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[  ]]></Span><Span Background="LightCoral"><![CDATA[<PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" />]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtInstall>5.9.9_msvc2015_64</QtInstall>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  </PropertyGroup>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtInstall>5.9.9_msvc2015_64</QtInstall>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  </PropertyGroup>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing Qt module macros from compiler properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;NDEBUG;QT_NO_DEBUG;]]></Span><Span Background="LightCoral"><![CDATA[QT_WIDGETS_LIB;QT_GUI_LIB;QT_SCRIPT_LIB;QT_SCRIPTTOOLS_LIB;QT_CORE_LIB;]]></Span><Span><![CDATA[NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;NDEBUG;QT_NO_DEBUG;]]></Span><Span><![CDATA[NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;DEBUG;]]></Span><Span Background="LightCoral"><![CDATA[QT_WIDGETS_LIB;QT_GUI_LIB;QT_SCRIPT_LIB;QT_SCRIPTTOOLS_LIB;QT_CORE_LIB;]]></Span><Span><![CDATA[%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;DEBUG;]]></Span><Span><![CDATA[%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing Qt module include paths from compiler properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;..\..\QtnProperty;]]></Span><Span Background="LightCoral"><![CDATA[$(QTDIR)\include;$(QTDIR)\include\QtWidgets;$(QTDIR)\include\QtGui;$(QTDIR)\include\QtANGLE;$(QTDIR)\include\QtScript;$(QTDIR)\include\QtScriptTools;$(QTDIR)\include\QtCore;$(QTDIR)\mkspecs\win32-msvc;]]></Span><Span><![CDATA[%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;..\..\QtnProperty;]]></Span><Span><![CDATA[%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;..\..\QtnProperty;]]></Span><Span Background="LightCoral"><![CDATA[$(QTDIR)\include;$(QTDIR)\include\QtWidgets;$(QTDIR)\include\QtGui;$(QTDIR)\include\QtANGLE;$(QTDIR)\include\QtScript;$(QTDIR)\include\QtScriptTools;$(QTDIR)\include\QtCore;$(QTDIR)\mkspecs\win32-msvc;]]></Span><Span><![CDATA[%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;..\..\QtnProperty;]]></Span><Span><![CDATA[%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing Qt module libraries from linker properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalDependencies>$(QTDIR)\lib\qtmain.lib;shell32]]></Span><Span><![CDATA[.lib;QtnProperty]]></Span><Span Background="LightCoral"><![CDATA[.lib;$(QTDIR)\lib\Qt5Widgets.lib;$(QTDIR)\lib\Qt5Gui.lib;$(QTDIR)\lib\Qt5Script.lib;$(QTDIR)\lib\Qt5ScriptTools.lib;$(QTDIR)\lib\Qt5Core]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalDependencies>shell32]]></Span><Span><![CDATA[.lib;QtnProperty]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalDependencies>$(QTDIR)\lib\qtmaind.lib;shell32]]></Span><Span><![CDATA[.lib;QtnProperty]]></Span><Span Background="LightCoral"><![CDATA[.lib;$(QTDIR)\lib\Qt5Widgetsd.lib;$(QTDIR)\lib\Qt5Guid.lib;$(QTDIR)\lib\Qt5Scriptd.lib;$(QTDIR)\lib\Qt5ScriptToolsd.lib;$(QTDIR)\lib\Qt5Cored]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalDependencies>shell32]]></Span><Span><![CDATA[.lib;QtnProperty]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing Qt lib path from linker properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalLibraryDirectories>$(QTDIR)\lib;C:\openssl\lib]]></Span><Span><![CDATA[;C:\Utils\my_sql\my_sql\lib;C:\Utils\postgresql\pgsql\lib]]></Span><Span Background="LightCoral"><![CDATA[;$(QTDIR)\lib]]></Span><Span><![CDATA[;G:\Code\Qt\QtnProperty\bin-win32-msvc1944-x86_64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalLibraryDirectories>C:\openssl\lib]]></Span><Span><![CDATA[;C:\Utils\my_sql\my_sql\lib;C:\Utils\postgresql\pgsql\lib]]></Span><Span><![CDATA[;G:\Code\Qt\QtnProperty\bin-win32-msvc1944-x86_64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalLibraryDirectories>$(QTDIR)\lib;C:\openssl\lib]]></Span><Span><![CDATA[;C:\Utils\my_sql\my_sql\lib;C:\Utils\postgresql\pgsql\lib]]></Span><Span Background="LightCoral"><![CDATA[;$(QTDIR)\lib]]></Span><Span><![CDATA[;G:\Code\Qt\QtnProperty\bin-win32-msvc1944-x86_64\debug;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalLibraryDirectories>C:\openssl\lib]]></Span><Span><![CDATA[;C:\Utils\my_sql\my_sql\lib;C:\Utils\postgresql\pgsql\lib]]></Span><Span><![CDATA[;G:\Code\Qt\QtnProperty\bin-win32-msvc1944-x86_64\debug;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing Qt module macros from resource compiler properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;NDEBUG;QT_NO_DEBUG;]]></Span><Span Background="LightCoral"><![CDATA[QT_WIDGETS_LIB;QT_GUI_LIB;QT_SCRIPT_LIB;QT_SCRIPTTOOLS_LIB;QT_CORE_LIB;]]></Span><Span><![CDATA[%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;NDEBUG;QT_NO_DEBUG;]]></Span><Span><![CDATA[%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;DEBUG;]]></Span><Span Background="LightCoral"><![CDATA[QT_WIDGETS_LIB;QT_GUI_LIB;QT_SCRIPT_LIB;QT_SCRIPTTOOLS_LIB;QT_CORE_LIB;]]></Span><Span><![CDATA[_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;DEBUG;]]></Span><Span><![CDATA[_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Adding Qt module names to QtModules project property]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtModules>core;gui;script;scripttools;widgets</QtModules>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtModules>core;gui;script;scripttools;widgets</QtModules>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Converting OutputFile to <tool>Dir and <tool>FileName]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtMocDir>$(ProjectDir)</QtMocDir>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtMocFileName>moc_%(Filename).cpp</QtMocFileName>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtUicDir>$(ProjectDir)</QtUicDir>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtUicFileName>ui_%(Filename).h</QtUicFileName>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtUicDir>$(ProjectDir)</QtUicDir>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtUicFileName>ui_%(Filename).h</QtUicFileName>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtMocDir>$(ProjectDir)</QtMocDir>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtMocFileName>moc_%(Filename).cpp</QtMocFileName>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing old properties from project items]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[G:\Code\Qt\QtnProperty\Demo\QtnPropertyDemo.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <OutputFile>$(ProjectDir)\moc_%(Filename).cpp</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Define>UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;NDEBUG;QT_NO_DEBUG;QT_WIDGETS_LIB;QT_GUI_LIB;QT_SCRIPT_LIB;QT_SCRIPTTOOLS_LIB;QT_CORE_LIB</Define>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <IncludePath>GeneratedFiles\$(ConfigurationName);GeneratedFiles;$(QTDIR)/mkspecs/win32-msvc;.;G:/Code/Qt/QtnProperty;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include;C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um</IncludePath>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <OutputFile>$(ProjectDir)\ui_%(Filename).h</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <OutputFile>$(ProjectDir)\ui_%(Filename).h</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <OutputFile>$(ProjectDir)\moc_%(Filename).cpp</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Define>UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;DEBUG;QT_WIDGETS_LIB;QT_GUI_LIB;QT_SCRIPT_LIB;QT_SCRIPTTOOLS_LIB;QT_CORE_LIB</Define>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <IncludePath>GeneratedFiles\$(ConfigurationName);GeneratedFiles;$(QTDIR)/mkspecs/win32-msvc;.;G:/Code/Qt/QtnProperty;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\ATLMFC\include;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\VS\include;C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um</IncludePath>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
  </Section>
  <Section>
    <Paragraph />
  </Section>
</FlowDocument>
<!--DAXiwE25ijRVJCPX8kLfHMD58N5l+a6vAX0qWdn3XpM=-->
