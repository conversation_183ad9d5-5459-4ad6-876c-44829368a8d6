/****************************************************************************
** Meta object code from reading C++ file 'PropertyDouble.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../Core/PropertyDouble.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'PropertyDouble.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtnPropertyDoubleBase_t {
    QByteArrayData data[1];
    char stringdata0[22];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyDoubleBase_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyDoubleBase_t qt_meta_stringdata_QtnPropertyDoubleBase = {
    {
QT_MOC_LITERAL(0, 0, 21) // "QtnPropertyDoubleBase"

    },
    "QtnPropertyDoubleBase"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyDoubleBase[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyDoubleBase::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyDoubleBase::staticMetaObject = {
    { &QtnNumericPropertyBase<QtnSinglePropertyBase<double>>::staticMetaObject, qt_meta_stringdata_QtnPropertyDoubleBase.data,
      qt_meta_data_QtnPropertyDoubleBase,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyDoubleBase::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyDoubleBase::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyDoubleBase.stringdata0))
        return static_cast<void*>(this);
    return QtnNumericPropertyBase<QtnSinglePropertyBase<double>>::qt_metacast(_clname);
}

int QtnPropertyDoubleBase::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnNumericPropertyBase<QtnSinglePropertyBase<double>>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyDoubleCallback_t {
    QByteArrayData data[3];
    char stringdata0[34];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyDoubleCallback_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyDoubleCallback_t qt_meta_stringdata_QtnPropertyDoubleCallback = {
    {
QT_MOC_LITERAL(0, 0, 25), // "QtnPropertyDoubleCallback"
QT_MOC_LITERAL(1, 26, 0), // ""
QT_MOC_LITERAL(2, 27, 6) // "parent"

    },
    "QtnPropertyDoubleCallback\0\0parent"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyDoubleCallback[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       2,   18, // constructors
       0,       // flags
       0,       // signalCount

 // constructors: parameters
    0x80000000 | 1, QMetaType::QObjectStar,    2,
    0x80000000 | 1,

 // constructors: name, argc, parameters, tag, flags
       0,    1,   14,    1, 0x0e /* Public */,
       0,    0,   17,    1, 0x2e /* Public | MethodCloned */,

       0        // eod
};

void QtnPropertyDoubleCallback::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::CreateInstance) {
        switch (_id) {
        case 0: { QtnPropertyDoubleCallback *_r = new QtnPropertyDoubleCallback((*reinterpret_cast< QObject*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        case 1: { QtnPropertyDoubleCallback *_r = new QtnPropertyDoubleCallback();
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        default: break;
        }
    }
    Q_UNUSED(_o);
}

const QMetaObject QtnPropertyDoubleCallback::staticMetaObject = {
    { &QtnSinglePropertyCallback<QtnPropertyDoubleBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyDoubleCallback.data,
      qt_meta_data_QtnPropertyDoubleCallback,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyDoubleCallback::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyDoubleCallback::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyDoubleCallback.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyCallback<QtnPropertyDoubleBase>::qt_metacast(_clname);
}

int QtnPropertyDoubleCallback::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyCallback<QtnPropertyDoubleBase>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyDouble_t {
    QByteArrayData data[3];
    char stringdata0[26];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyDouble_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyDouble_t qt_meta_stringdata_QtnPropertyDouble = {
    {
QT_MOC_LITERAL(0, 0, 17), // "QtnPropertyDouble"
QT_MOC_LITERAL(1, 18, 0), // ""
QT_MOC_LITERAL(2, 19, 6) // "parent"

    },
    "QtnPropertyDouble\0\0parent"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyDouble[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       2,   18, // constructors
       0,       // flags
       0,       // signalCount

 // constructors: parameters
    0x80000000 | 1, QMetaType::QObjectStar,    2,
    0x80000000 | 1,

 // constructors: name, argc, parameters, tag, flags
       0,    1,   14,    1, 0x0e /* Public */,
       0,    0,   17,    1, 0x2e /* Public | MethodCloned */,

       0        // eod
};

void QtnPropertyDouble::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::CreateInstance) {
        switch (_id) {
        case 0: { QtnPropertyDouble *_r = new QtnPropertyDouble((*reinterpret_cast< QObject*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        case 1: { QtnPropertyDouble *_r = new QtnPropertyDouble();
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        default: break;
        }
    }
    Q_UNUSED(_o);
}

const QMetaObject QtnPropertyDouble::staticMetaObject = {
    { &QtnNumericPropertyValue<QtnPropertyDoubleBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyDouble.data,
      qt_meta_data_QtnPropertyDouble,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyDouble::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyDouble::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyDouble.stringdata0))
        return static_cast<void*>(this);
    return QtnNumericPropertyValue<QtnPropertyDoubleBase>::qt_metacast(_clname);
}

int QtnPropertyDouble::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnNumericPropertyValue<QtnPropertyDoubleBase>::qt_metacall(_c, _id, _a);
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
