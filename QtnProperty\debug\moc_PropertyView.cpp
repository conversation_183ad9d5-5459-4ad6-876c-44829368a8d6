/****************************************************************************
** Meta object code from reading C++ file 'PropertyView.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../PropertyView.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'PropertyView.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtnPropertyView_t {
    QByteArrayData data[21];
    char stringdata0[318];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyView_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyView_t qt_meta_stringdata_QtnPropertyView = {
    {
QT_MOC_LITERAL(0, 0, 15), // "QtnPropertyView"
QT_MOC_LITERAL(1, 16, 17), // "propertiesChanged"
QT_MOC_LITERAL(2, 34, 0), // ""
QT_MOC_LITERAL(3, 35, 23), // "QtnPropertyChangeReason"
QT_MOC_LITERAL(4, 59, 6), // "reason"
QT_MOC_LITERAL(5, 66, 21), // "activePropertyChanged"
QT_MOC_LITERAL(6, 88, 16), // "QtnPropertyBase*"
QT_MOC_LITERAL(7, 105, 14), // "activeProperty"
QT_MOC_LITERAL(8, 120, 13), // "mouseReleased"
QT_MOC_LITERAL(9, 134, 12), // "QMouseEvent*"
QT_MOC_LITERAL(10, 147, 1), // "e"
QT_MOC_LITERAL(11, 149, 20), // "beforePropertyEdited"
QT_MOC_LITERAL(12, 170, 8), // "property"
QT_MOC_LITERAL(13, 179, 19), // "QtnPropertyValuePtr"
QT_MOC_LITERAL(14, 199, 8), // "newValue"
QT_MOC_LITERAL(15, 208, 6), // "typeId"
QT_MOC_LITERAL(16, 215, 14), // "propertyEdited"
QT_MOC_LITERAL(17, 230, 25), // "beforePropertyLockToggled"
QT_MOC_LITERAL(18, 256, 19), // "propertyLockToggled"
QT_MOC_LITERAL(19, 276, 18), // "accessibilityProxy"
QT_MOC_LITERAL(20, 295, 22) // "QtnAccessibilityProxy*"

    },
    "QtnPropertyView\0propertiesChanged\0\0"
    "QtnPropertyChangeReason\0reason\0"
    "activePropertyChanged\0QtnPropertyBase*\0"
    "activeProperty\0mouseReleased\0QMouseEvent*\0"
    "e\0beforePropertyEdited\0property\0"
    "QtnPropertyValuePtr\0newValue\0typeId\0"
    "propertyEdited\0beforePropertyLockToggled\0"
    "propertyLockToggled\0accessibilityProxy\0"
    "QtnAccessibilityProxy*"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyView[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       8,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       7,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   54,    2, 0x06 /* Public */,
       5,    1,   57,    2, 0x06 /* Public */,
       8,    1,   60,    2, 0x06 /* Public */,
      11,    3,   63,    2, 0x06 /* Public */,
      16,    1,   70,    2, 0x06 /* Public */,
      17,    1,   73,    2, 0x06 /* Public */,
      18,    1,   76,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      19,    0,   79,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 6,    7,
    QMetaType::Void, 0x80000000 | 9,   10,
    QMetaType::Void, 0x80000000 | 6, 0x80000000 | 13, QMetaType::Int,   12,   14,   15,
    QMetaType::Void, 0x80000000 | 6,   12,
    QMetaType::Void, 0x80000000 | 6,   12,
    QMetaType::Void, 0x80000000 | 6,   12,

 // slots: parameters
    0x80000000 | 20,

       0        // eod
};

void QtnPropertyView::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        QtnPropertyView *_t = static_cast<QtnPropertyView *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->propertiesChanged((*reinterpret_cast< QtnPropertyChangeReason(*)>(_a[1]))); break;
        case 1: _t->activePropertyChanged((*reinterpret_cast< QtnPropertyBase*(*)>(_a[1]))); break;
        case 2: _t->mouseReleased((*reinterpret_cast< QMouseEvent*(*)>(_a[1]))); break;
        case 3: _t->beforePropertyEdited((*reinterpret_cast< QtnPropertyBase*(*)>(_a[1])),(*reinterpret_cast< QtnPropertyValuePtr(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3]))); break;
        case 4: _t->propertyEdited((*reinterpret_cast< QtnPropertyBase*(*)>(_a[1]))); break;
        case 5: _t->beforePropertyLockToggled((*reinterpret_cast< QtnPropertyBase*(*)>(_a[1]))); break;
        case 6: _t->propertyLockToggled((*reinterpret_cast< QtnPropertyBase*(*)>(_a[1]))); break;
        case 7: { QtnAccessibilityProxy* _r = _t->accessibilityProxy();
            if (_a[0]) *reinterpret_cast< QtnAccessibilityProxy**>(_a[0]) = std::move(_r); }  break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 0:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyChangeReason >(); break;
            }
            break;
        case 1:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyBase* >(); break;
            }
            break;
        case 3:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyBase* >(); break;
            case 1:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyValuePtr >(); break;
            }
            break;
        case 4:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyBase* >(); break;
            }
            break;
        case 5:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyBase* >(); break;
            }
            break;
        case 6:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyBase* >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            typedef void (QtnPropertyView::*_t)(QtnPropertyChangeReason );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtnPropertyView::propertiesChanged)) {
                *result = 0;
                return;
            }
        }
        {
            typedef void (QtnPropertyView::*_t)(QtnPropertyBase * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtnPropertyView::activePropertyChanged)) {
                *result = 1;
                return;
            }
        }
        {
            typedef void (QtnPropertyView::*_t)(QMouseEvent * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtnPropertyView::mouseReleased)) {
                *result = 2;
                return;
            }
        }
        {
            typedef void (QtnPropertyView::*_t)(QtnPropertyBase * , QtnPropertyValuePtr , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtnPropertyView::beforePropertyEdited)) {
                *result = 3;
                return;
            }
        }
        {
            typedef void (QtnPropertyView::*_t)(QtnPropertyBase * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtnPropertyView::propertyEdited)) {
                *result = 4;
                return;
            }
        }
        {
            typedef void (QtnPropertyView::*_t)(QtnPropertyBase * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtnPropertyView::beforePropertyLockToggled)) {
                *result = 5;
                return;
            }
        }
        {
            typedef void (QtnPropertyView::*_t)(QtnPropertyBase * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtnPropertyView::propertyLockToggled)) {
                *result = 6;
                return;
            }
        }
    }
}

const QMetaObject QtnPropertyView::staticMetaObject = {
    { &QAbstractScrollArea::staticMetaObject, qt_meta_stringdata_QtnPropertyView.data,
      qt_meta_data_QtnPropertyView,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyView::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyView::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyView.stringdata0))
        return static_cast<void*>(this);
    return QAbstractScrollArea::qt_metacast(_clname);
}

int QtnPropertyView::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QAbstractScrollArea::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    }
    return _id;
}

// SIGNAL 0
void QtnPropertyView::propertiesChanged(QtnPropertyChangeReason _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void QtnPropertyView::activePropertyChanged(QtnPropertyBase * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void QtnPropertyView::mouseReleased(QMouseEvent * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void QtnPropertyView::beforePropertyEdited(QtnPropertyBase * _t1, QtnPropertyValuePtr _t2, int _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)), const_cast<void*>(reinterpret_cast<const void*>(&_t3)) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void QtnPropertyView::propertyEdited(QtnPropertyBase * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void QtnPropertyView::beforePropertyLockToggled(QtnPropertyBase * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void QtnPropertyView::propertyLockToggled(QtnPropertyBase * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
