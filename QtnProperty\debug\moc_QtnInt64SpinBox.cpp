/****************************************************************************
** Meta object code from reading C++ file 'QtnInt64SpinBox.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../Utils/QtnInt64SpinBox.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'QtnInt64SpinBox.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtnInt64SpinBox_t {
    QByteArrayData data[15];
    char stringdata0[151];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnInt64SpinBox_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnInt64SpinBox_t qt_meta_stringdata_QtnInt64SpinBox = {
    {
QT_MOC_LITERAL(0, 0, 15), // "QtnInt64SpinBox"
QT_MOC_LITERAL(1, 16, 12), // "valueChanged"
QT_MOC_LITERAL(2, 29, 0), // ""
QT_MOC_LITERAL(3, 30, 8), // "setValue"
QT_MOC_LITERAL(4, 39, 3), // "val"
QT_MOC_LITERAL(5, 43, 6), // "suffix"
QT_MOC_LITERAL(6, 50, 6), // "prefix"
QT_MOC_LITERAL(7, 57, 9), // "cleanText"
QT_MOC_LITERAL(8, 67, 7), // "minimum"
QT_MOC_LITERAL(9, 75, 7), // "maximum"
QT_MOC_LITERAL(10, 83, 10), // "singleStep"
QT_MOC_LITERAL(11, 94, 5), // "value"
QT_MOC_LITERAL(12, 100, 18), // "displayIntegerBase"
QT_MOC_LITERAL(13, 119, 14), // "isSpecialValue"
QT_MOC_LITERAL(14, 134, 16) // "specialValueText"

    },
    "QtnInt64SpinBox\0valueChanged\0\0setValue\0"
    "val\0suffix\0prefix\0cleanText\0minimum\0"
    "maximum\0singleStep\0value\0displayIntegerBase\0"
    "isSpecialValue\0specialValueText"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnInt64SpinBox[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       3,   14, // methods
      10,   38, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   29,    2, 0x06 /* Public */,
       1,    1,   32,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       3,    1,   35,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::LongLong,    2,
    QMetaType::Void, QMetaType::QString,    2,

 // slots: parameters
    QMetaType::Void, QMetaType::LongLong,    4,

 // properties: name, type, flags
       5, QMetaType::QString, 0x00095103,
       6, QMetaType::QString, 0x00095103,
       7, QMetaType::QString, 0x00095001,
       8, QMetaType::LongLong, 0x00095103,
       9, QMetaType::LongLong, 0x00095103,
      10, QMetaType::LongLong, 0x00095103,
      11, QMetaType::LongLong, 0x00595103,
      12, QMetaType::Int, 0x00095103,
      13, QMetaType::Bool, 0x00095001,
      14, QMetaType::QString, 0x00095103,

 // properties: notify_signal_id
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,

       0        // eod
};

void QtnInt64SpinBox::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        QtnInt64SpinBox *_t = static_cast<QtnInt64SpinBox *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->valueChanged((*reinterpret_cast< qint64(*)>(_a[1]))); break;
        case 1: _t->valueChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 2: _t->setValue((*reinterpret_cast< qint64(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            typedef void (QtnInt64SpinBox::*_t)(qint64 );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtnInt64SpinBox::valueChanged)) {
                *result = 0;
                return;
            }
        }
        {
            typedef void (QtnInt64SpinBox::*_t)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtnInt64SpinBox::valueChanged)) {
                *result = 1;
                return;
            }
        }
    }
#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty) {
        QtnInt64SpinBox *_t = static_cast<QtnInt64SpinBox *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QString*>(_v) = _t->suffix(); break;
        case 1: *reinterpret_cast< QString*>(_v) = _t->prefix(); break;
        case 2: *reinterpret_cast< QString*>(_v) = _t->cleanText(); break;
        case 3: *reinterpret_cast< qint64*>(_v) = _t->minimum(); break;
        case 4: *reinterpret_cast< qint64*>(_v) = _t->maximum(); break;
        case 5: *reinterpret_cast< qint64*>(_v) = _t->singleStep(); break;
        case 6: *reinterpret_cast< qint64*>(_v) = _t->value(); break;
        case 7: *reinterpret_cast< int*>(_v) = _t->displayIntegerBase(); break;
        case 8: *reinterpret_cast< bool*>(_v) = _t->isSpecialValue(); break;
        case 9: *reinterpret_cast< QString*>(_v) = _t->specialValueText(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        QtnInt64SpinBox *_t = static_cast<QtnInt64SpinBox *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setSuffix(*reinterpret_cast< QString*>(_v)); break;
        case 1: _t->setPrefix(*reinterpret_cast< QString*>(_v)); break;
        case 3: _t->setMinimum(*reinterpret_cast< qint64*>(_v)); break;
        case 4: _t->setMaximum(*reinterpret_cast< qint64*>(_v)); break;
        case 5: _t->setSingleStep(*reinterpret_cast< qint64*>(_v)); break;
        case 6: _t->setValue(*reinterpret_cast< qint64*>(_v)); break;
        case 7: _t->setDisplayIntegerBase(*reinterpret_cast< int*>(_v)); break;
        case 9: _t->setSpecialValueText(*reinterpret_cast< QString*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
}

const QMetaObject QtnInt64SpinBox::staticMetaObject = {
    { &QAbstractSpinBox::staticMetaObject, qt_meta_stringdata_QtnInt64SpinBox.data,
      qt_meta_data_QtnInt64SpinBox,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnInt64SpinBox::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnInt64SpinBox::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnInt64SpinBox.stringdata0))
        return static_cast<void*>(this);
    return QAbstractSpinBox::qt_metacast(_clname);
}

int QtnInt64SpinBox::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QAbstractSpinBox::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 3)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 3)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 3;
    }
#ifndef QT_NO_PROPERTIES
   else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 10;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 10;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 10;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 10;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 10;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}

// SIGNAL 0
void QtnInt64SpinBox::valueChanged(qint64 _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void QtnInt64SpinBox::valueChanged(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
