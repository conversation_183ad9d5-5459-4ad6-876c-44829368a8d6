/****************************************************************************
** Meta object code from reading C++ file 'PropertyQBrush.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../GUI/PropertyQBrush.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'PropertyQBrush.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtnPropertyQBrushStyleBase_t {
    QByteArrayData data[1];
    char stringdata0[27];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQBrushStyleBase_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQBrushStyleBase_t qt_meta_stringdata_QtnPropertyQBrushStyleBase = {
    {
QT_MOC_LITERAL(0, 0, 26) // "QtnPropertyQBrushStyleBase"

    },
    "QtnPropertyQBrushStyleBase"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQBrushStyleBase[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyQBrushStyleBase::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyQBrushStyleBase::staticMetaObject = {
    { &QtnSinglePropertyBase<Qt::BrushStyle>::staticMetaObject, qt_meta_stringdata_QtnPropertyQBrushStyleBase.data,
      qt_meta_data_QtnPropertyQBrushStyleBase,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQBrushStyleBase::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQBrushStyleBase::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQBrushStyleBase.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyBase<Qt::BrushStyle>::qt_metacast(_clname);
}

int QtnPropertyQBrushStyleBase::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyBase<Qt::BrushStyle>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyQBrushStyleCallback_t {
    QByteArrayData data[1];
    char stringdata0[31];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQBrushStyleCallback_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQBrushStyleCallback_t qt_meta_stringdata_QtnPropertyQBrushStyleCallback = {
    {
QT_MOC_LITERAL(0, 0, 30) // "QtnPropertyQBrushStyleCallback"

    },
    "QtnPropertyQBrushStyleCallback"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQBrushStyleCallback[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyQBrushStyleCallback::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyQBrushStyleCallback::staticMetaObject = {
    { &QtnSinglePropertyCallback<QtnPropertyQBrushStyleBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyQBrushStyleCallback.data,
      qt_meta_data_QtnPropertyQBrushStyleCallback,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQBrushStyleCallback::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQBrushStyleCallback::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQBrushStyleCallback.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyCallback<QtnPropertyQBrushStyleBase>::qt_metacast(_clname);
}

int QtnPropertyQBrushStyleCallback::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyCallback<QtnPropertyQBrushStyleBase>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyQBrushStyle_t {
    QByteArrayData data[1];
    char stringdata0[23];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQBrushStyle_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQBrushStyle_t qt_meta_stringdata_QtnPropertyQBrushStyle = {
    {
QT_MOC_LITERAL(0, 0, 22) // "QtnPropertyQBrushStyle"

    },
    "QtnPropertyQBrushStyle"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQBrushStyle[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyQBrushStyle::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyQBrushStyle::staticMetaObject = {
    { &QtnSinglePropertyValue<QtnPropertyQBrushStyleBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyQBrushStyle.data,
      qt_meta_data_QtnPropertyQBrushStyle,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQBrushStyle::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQBrushStyle::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQBrushStyle.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyValue<QtnPropertyQBrushStyleBase>::qt_metacast(_clname);
}

int QtnPropertyQBrushStyle::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyValue<QtnPropertyQBrushStyleBase>::qt_metacall(_c, _id, _a);
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
