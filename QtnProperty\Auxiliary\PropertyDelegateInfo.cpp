/*******************************************************************************
Copyright (c) 2012-2016 <PERSON> <<EMAIL>>
Copyright (c) 2015-2019 <PERSON> <<EMAIL>>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*******************************************************************************/

#include "PropertyDelegateInfo.h"
#include "QtnProperty/PropertyDelegateAttrs.h"
#include "PropertyMacro.h"

QtnPropertyDelegateInfo::QtnPropertyDelegateInfo(
	const QtnPropertyDelegateInfo &other)
	: name(other.name)
	, attributes(other.attributes)
{
}

QtnPropertyDelegateInfo::QtnPropertyDelegateInfo(
	const QByteArray &name, const Attributes &attributes)
	: name(name)
	, attributes(attributes)
{
}

QByteArray qtnComboBoxDelegate()
{
	return QByteArrayLiteral("ComboBox");
}

QByteArray qtnCheckBoxDelegate()
{
	return QByteArrayLiteral("CheckBox");
}

QByteArray qtnFieldDelegateNameAttr()
{
	return QByteArrayLiteral("fieldDelegateName");
}

double qtnHundredPercent(double value)
{
	return std::max(0.0, std::min(100.0, value));
}
