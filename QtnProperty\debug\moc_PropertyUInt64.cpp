/****************************************************************************
** Meta object code from reading C++ file 'PropertyUInt64.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../PropertyUInt64.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'PropertyUInt64.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtnPropertyUInt64Base_t {
    QByteArrayData data[1];
    char stringdata0[22];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyUInt64Base_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyUInt64Base_t qt_meta_stringdata_QtnPropertyUInt64Base = {
    {
QT_MOC_LITERAL(0, 0, 21) // "QtnPropertyUInt64Base"

    },
    "QtnPropertyUInt64Base"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyUInt64Base[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyUInt64Base::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyUInt64Base::staticMetaObject = {
    { &QtnNumericPropertyBase<QtnSinglePropertyBase<quint64>>::staticMetaObject, qt_meta_stringdata_QtnPropertyUInt64Base.data,
      qt_meta_data_QtnPropertyUInt64Base,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyUInt64Base::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyUInt64Base::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyUInt64Base.stringdata0))
        return static_cast<void*>(this);
    return QtnNumericPropertyBase<QtnSinglePropertyBase<quint64>>::qt_metacast(_clname);
}

int QtnPropertyUInt64Base::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnNumericPropertyBase<QtnSinglePropertyBase<quint64>>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyUInt64Callback_t {
    QByteArrayData data[1];
    char stringdata0[26];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyUInt64Callback_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyUInt64Callback_t qt_meta_stringdata_QtnPropertyUInt64Callback = {
    {
QT_MOC_LITERAL(0, 0, 25) // "QtnPropertyUInt64Callback"

    },
    "QtnPropertyUInt64Callback"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyUInt64Callback[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyUInt64Callback::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyUInt64Callback::staticMetaObject = {
    { &QtnSinglePropertyCallback<QtnPropertyUInt64Base>::staticMetaObject, qt_meta_stringdata_QtnPropertyUInt64Callback.data,
      qt_meta_data_QtnPropertyUInt64Callback,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyUInt64Callback::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyUInt64Callback::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyUInt64Callback.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyCallback<QtnPropertyUInt64Base>::qt_metacast(_clname);
}

int QtnPropertyUInt64Callback::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyCallback<QtnPropertyUInt64Base>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyUInt64_t {
    QByteArrayData data[1];
    char stringdata0[18];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyUInt64_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyUInt64_t qt_meta_stringdata_QtnPropertyUInt64 = {
    {
QT_MOC_LITERAL(0, 0, 17) // "QtnPropertyUInt64"

    },
    "QtnPropertyUInt64"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyUInt64[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyUInt64::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyUInt64::staticMetaObject = {
    { &QtnNumericPropertyValue<QtnPropertyUInt64Base>::staticMetaObject, qt_meta_stringdata_QtnPropertyUInt64.data,
      qt_meta_data_QtnPropertyUInt64,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyUInt64::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyUInt64::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyUInt64.stringdata0))
        return static_cast<void*>(this);
    return QtnNumericPropertyValue<QtnPropertyUInt64Base>::qt_metacast(_clname);
}

int QtnPropertyUInt64::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnNumericPropertyValue<QtnPropertyUInt64Base>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyDelegateUInt64_t {
    QByteArrayData data[1];
    char stringdata0[26];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyDelegateUInt64_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyDelegateUInt64_t qt_meta_stringdata_QtnPropertyDelegateUInt64 = {
    {
QT_MOC_LITERAL(0, 0, 25) // "QtnPropertyDelegateUInt64"

    },
    "QtnPropertyDelegateUInt64"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyDelegateUInt64[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyDelegateUInt64::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyDelegateUInt64::staticMetaObject = {
    { &QObject::staticMetaObject, qt_meta_stringdata_QtnPropertyDelegateUInt64.data,
      qt_meta_data_QtnPropertyDelegateUInt64,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyDelegateUInt64::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyDelegateUInt64::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyDelegateUInt64.stringdata0))
        return static_cast<void*>(this);
    if (!strcmp(_clname, "QtnPropertyDelegateTyped<QtnPropertyUInt64Base>"))
        return static_cast< QtnPropertyDelegateTyped<QtnPropertyUInt64Base>*>(this);
    return QObject::qt_metacast(_clname);
}

int QtnPropertyDelegateUInt64::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
