/********************************************************************************
** Form generated from reading UI file 'CustomPropertyEditorDialog.ui'
**
** Created by: Qt User Interface Compiler version 5.9.9
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_CUSTOMPROPERTYEDITORDIALOG_H
#define UI_CUSTOMPROPERTYEDITORDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QDialog>
#include <QtWidgets/QDialogButtonBox>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QVBoxLayout>
#include "QtnProperty/CustomPropertyWidget.h"

QT_BEGIN_NAMESPACE

class Ui_CustomPropertyEditorDialog
{
public:
    QAction *actionPropertyAdd;
    QAction *actionPropertyDelete;
    QAction *actionPropertyDuplicate;
    QAction *actionPropertyOptions;
    QAction *actionPropertyCut;
    QAction *actionPropertyCopy;
    QAction *actionPropertyPaste;
    QVBoxLayout *verticalLayout;
    QtnCustomPropertyWidget *propertyWidget;
    QDialogButtonBox *buttonBox;

    void setupUi(QDialog *CustomPropertyEditorDialog)
    {
        if (CustomPropertyEditorDialog->objectName().isEmpty())
            CustomPropertyEditorDialog->setObjectName(QStringLiteral("CustomPropertyEditorDialog"));
        CustomPropertyEditorDialog->resize(389, 468);
        CustomPropertyEditorDialog->setWindowTitle(QStringLiteral(""));
        CustomPropertyEditorDialog->setSizeGripEnabled(true);
        CustomPropertyEditorDialog->setModal(true);
        actionPropertyAdd = new QAction(CustomPropertyEditorDialog);
        actionPropertyAdd->setObjectName(QStringLiteral("actionPropertyAdd"));
        actionPropertyAdd->setText(QStringLiteral(""));
#ifndef QT_NO_TOOLTIP
        actionPropertyAdd->setToolTip(QStringLiteral(""));
#endif // QT_NO_TOOLTIP
#ifndef QT_NO_SHORTCUT
        actionPropertyAdd->setShortcut(QStringLiteral("Ctrl+="));
#endif // QT_NO_SHORTCUT
        actionPropertyAdd->setShortcutContext(Qt::WindowShortcut);
        actionPropertyDelete = new QAction(CustomPropertyEditorDialog);
        actionPropertyDelete->setObjectName(QStringLiteral("actionPropertyDelete"));
#ifndef QT_NO_SHORTCUT
        actionPropertyDelete->setShortcut(QStringLiteral("Del"));
#endif // QT_NO_SHORTCUT
        actionPropertyDelete->setShortcutContext(Qt::WindowShortcut);
        actionPropertyDuplicate = new QAction(CustomPropertyEditorDialog);
        actionPropertyDuplicate->setObjectName(QStringLiteral("actionPropertyDuplicate"));
#ifndef QT_NO_TOOLTIP
        actionPropertyDuplicate->setToolTip(QStringLiteral("Duplicate"));
#endif // QT_NO_TOOLTIP
        actionPropertyOptions = new QAction(CustomPropertyEditorDialog);
        actionPropertyOptions->setObjectName(QStringLiteral("actionPropertyOptions"));
#ifndef QT_NO_TOOLTIP
        actionPropertyOptions->setToolTip(QStringLiteral("Options"));
#endif // QT_NO_TOOLTIP
#ifndef QT_NO_SHORTCUT
        actionPropertyOptions->setShortcut(QStringLiteral("F2"));
#endif // QT_NO_SHORTCUT
        actionPropertyOptions->setShortcutContext(Qt::WindowShortcut);
        actionPropertyCut = new QAction(CustomPropertyEditorDialog);
        actionPropertyCut->setObjectName(QStringLiteral("actionPropertyCut"));
#ifndef QT_NO_SHORTCUT
        actionPropertyCut->setShortcut(QStringLiteral("Ctrl+X"));
#endif // QT_NO_SHORTCUT
        actionPropertyCut->setShortcutContext(Qt::WindowShortcut);
        actionPropertyCopy = new QAction(CustomPropertyEditorDialog);
        actionPropertyCopy->setObjectName(QStringLiteral("actionPropertyCopy"));
#ifndef QT_NO_SHORTCUT
        actionPropertyCopy->setShortcut(QStringLiteral("Ctrl+C"));
#endif // QT_NO_SHORTCUT
        actionPropertyCopy->setShortcutContext(Qt::WindowShortcut);
        actionPropertyPaste = new QAction(CustomPropertyEditorDialog);
        actionPropertyPaste->setObjectName(QStringLiteral("actionPropertyPaste"));
#ifndef QT_NO_SHORTCUT
        actionPropertyPaste->setShortcut(QStringLiteral("Ctrl+V"));
#endif // QT_NO_SHORTCUT
        actionPropertyPaste->setShortcutContext(Qt::WindowShortcut);
        verticalLayout = new QVBoxLayout(CustomPropertyEditorDialog);
        verticalLayout->setSpacing(0);
        verticalLayout->setObjectName(QStringLiteral("verticalLayout"));
        verticalLayout->setContentsMargins(6, 6, 6, 6);
        propertyWidget = new QtnCustomPropertyWidget(CustomPropertyEditorDialog);
        propertyWidget->setObjectName(QStringLiteral("propertyWidget"));
        propertyWidget->setFocusPolicy(Qt::StrongFocus);
        propertyWidget->setContextMenuPolicy(Qt::CustomContextMenu);
        propertyWidget->setAcceptDrops(true);

        verticalLayout->addWidget(propertyWidget);

        buttonBox = new QDialogButtonBox(CustomPropertyEditorDialog);
        buttonBox->setObjectName(QStringLiteral("buttonBox"));
        buttonBox->setFocusPolicy(Qt::StrongFocus);
        buttonBox->setStandardButtons(QDialogButtonBox::Cancel|QDialogButtonBox::Ok);

        verticalLayout->addWidget(buttonBox);


        retranslateUi(CustomPropertyEditorDialog);

        QMetaObject::connectSlotsByName(CustomPropertyEditorDialog);
    } // setupUi

    void retranslateUi(QDialog *CustomPropertyEditorDialog)
    {
        actionPropertyDelete->setText(QApplication::translate("CustomPropertyEditorDialog", "Delete", Q_NULLPTR));
        actionPropertyDuplicate->setText(QApplication::translate("CustomPropertyEditorDialog", "Duplicate...", Q_NULLPTR));
        actionPropertyOptions->setText(QApplication::translate("CustomPropertyEditorDialog", "Options...", Q_NULLPTR));
#ifndef QT_NO_STATUSTIP
        actionPropertyOptions->setStatusTip(QString());
#endif // QT_NO_STATUSTIP
        actionPropertyCut->setText(QApplication::translate("CustomPropertyEditorDialog", "Cut", Q_NULLPTR));
        actionPropertyCopy->setText(QApplication::translate("CustomPropertyEditorDialog", "Copy", Q_NULLPTR));
        actionPropertyPaste->setText(QApplication::translate("CustomPropertyEditorDialog", "Paste", Q_NULLPTR));
        Q_UNUSED(CustomPropertyEditorDialog);
    } // retranslateUi

};

namespace Ui {
    class CustomPropertyEditorDialog: public Ui_CustomPropertyEditorDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_CUSTOMPROPERTYEDITORDIALOG_H
