PropertyArray

Implement PropertySetForm - property widgets on form

Make wrapper propertyset for QObject derived classes (implement Enum and EnumFlags cases)

Save/Load via XML

PropertyTypes:
1. Date/Time
2. Bitmap ???
4. Cursor
5. Icon

owner feature????
How to define default PropertySet?

command line parameters for PEG
refactor PEG to support bison 3.0
refactor PEG to generate thread-safe property_set constructors (static variables)
QtDesigner plugin
Help
pef files syntax highlight
CMake support

Tests:
2. Property equal (==)
3. Property <, > for numerics
4. make PropertySetAllPropertyTypes with subproperties
5. add benchmarks

implement ReportError method
