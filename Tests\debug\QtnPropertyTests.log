﻿Build started 8/7/2025 3:50:27 PM.
Logging verbosity is set to: Normal.     1>Project "G:\Code\Qt\QtnProperty\Tests\QtnPropertyTests.vcxproj" on node 2 (Build target(s)).
     1>InitializeBuildStatus:
         Creating "debug\QtnPropertyTests.tlog\unsuccessfulbuild" because "AlwaysCreate" was specified.
         Touching "debug\QtnPropertyTests.tlog\unsuccessfulbuild".
       CustomBuild:
         Generate moc_predefs.h
         PropertyEnum generator PEG/test.pef, PEG Headers PEG/test.pef
         'G:/Code/Qt/QtnProperty/Internal/../bin-win32-msvc1944-x86_64/debug/QtnPEG.exe' is not recognized as an internal or external command,
         operable program or batch file.
     1>C:\Program Files (x86)\MSBuild\Microsoft.Cpp\v4.0\V140\Microsoft.CppCommon.targets(171,5): error MSB6006: "cmd.exe" exited with code 9009.
     1>Done Building Project "G:\Code\Qt\QtnProperty\Tests\QtnPropertyTests.vcxproj" (Build target(s)) -- FAILED.

Build FAILED.

       "G:\Code\Qt\QtnProperty\Tests\QtnPropertyTests.vcxproj" (Build target) (1) ->
       (CustomBuild target) -> 
         C:\Program Files (x86)\MSBuild\Microsoft.Cpp\v4.0\V140\Microsoft.CppCommon.targets(171,5): error MSB6006: "cmd.exe" exited with code 9009.

    0 Warning(s)
    1 Error(s)

Time Elapsed 00:00:00.09
