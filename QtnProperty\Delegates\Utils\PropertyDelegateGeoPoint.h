/*******************************************************************************
Copyright (c) 2012-2016 <PERSON> <<EMAIL>>
Copyright (c) 2015-2019 <PERSON> <<EMAIL>>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*******************************************************************************/

#ifndef PROPERTY_DELEGATE_GEOPOINT_H
#define PROPERTY_DELEGATE_GEOPOINT_H

#include "QtnProperty/Delegates/Utils/PropertyDelegateMisc.h"
#include "QtnProperty/Core/PropertyQPointF.h"

class QTN_IMPORT_EXPORT QtnPropertyDelegateGeoPoint
	: public QtnPropertyDelegateTypedEx<QtnPropertyQPointFBase>
{
	Q_DISABLE_COPY(QtnPropertyDelegateGeoPoint)

public:
	QtnPropertyDelegateGeoPoint(QtnPropertyQPointFBase &owner);

	static void Register(QtnPropertyDelegateFactory &factory);

	static QString longitudeKey();
	static QString longitudeDisplayName();
	static QString longitudeDescriptionFmt();

	static QString latitudeKey();
	static QString latitudeDisplayName();
	static QString latitudeDescriptionFmt();

protected:
	virtual void applyAttributesImpl(
		const QtnPropertyDelegateInfo &info) override;

	virtual QWidget *createValueEditorImpl(QWidget *parent, const QRect &rect,
		QtnInplaceInfo *inplaceInfo = nullptr) override;

	virtual bool propertyValueToStrImpl(QString &strValue) const override;
};

#endif // PROPERTY_DELEGATE_GEOPOINT_H
