/****************************************************************************
** Meta object code from reading C++ file 'PropertyQVariant.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../PropertyQVariant.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'PropertyQVariant.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtnPropertyQVariantBase_t {
    QByteArrayData data[1];
    char stringdata0[24];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQVariantBase_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQVariantBase_t qt_meta_stringdata_QtnPropertyQVariantBase = {
    {
QT_MOC_LITERAL(0, 0, 23) // "QtnPropertyQVariantBase"

    },
    "QtnPropertyQVariantBase"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQVariantBase[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyQVariantBase::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyQVariantBase::staticMetaObject = {
    { &QtnSinglePropertyBase<QVariant>::staticMetaObject, qt_meta_stringdata_QtnPropertyQVariantBase.data,
      qt_meta_data_QtnPropertyQVariantBase,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQVariantBase::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQVariantBase::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQVariantBase.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyBase<QVariant>::qt_metacast(_clname);
}

int QtnPropertyQVariantBase::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyBase<QVariant>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyQVariantCallback_t {
    QByteArrayData data[1];
    char stringdata0[28];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQVariantCallback_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQVariantCallback_t qt_meta_stringdata_QtnPropertyQVariantCallback = {
    {
QT_MOC_LITERAL(0, 0, 27) // "QtnPropertyQVariantCallback"

    },
    "QtnPropertyQVariantCallback"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQVariantCallback[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyQVariantCallback::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyQVariantCallback::staticMetaObject = {
    { &QtnSinglePropertyCallback<QtnPropertyQVariantBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyQVariantCallback.data,
      qt_meta_data_QtnPropertyQVariantCallback,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQVariantCallback::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQVariantCallback::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQVariantCallback.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyCallback<QtnPropertyQVariantBase>::qt_metacast(_clname);
}

int QtnPropertyQVariantCallback::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyCallback<QtnPropertyQVariantBase>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyQVariant_t {
    QByteArrayData data[1];
    char stringdata0[20];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQVariant_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQVariant_t qt_meta_stringdata_QtnPropertyQVariant = {
    {
QT_MOC_LITERAL(0, 0, 19) // "QtnPropertyQVariant"

    },
    "QtnPropertyQVariant"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQVariant[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyQVariant::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyQVariant::staticMetaObject = {
    { &QtnSinglePropertyValue<QtnPropertyQVariantBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyQVariant.data,
      qt_meta_data_QtnPropertyQVariant,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQVariant::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQVariant::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQVariant.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyValue<QtnPropertyQVariantBase>::qt_metacast(_clname);
}

int QtnPropertyQVariant::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyValue<QtnPropertyQVariantBase>::qt_metacall(_c, _id, _a);
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
