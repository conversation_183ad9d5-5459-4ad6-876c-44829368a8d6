<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CustomPropertyEditorDialog</class>
 <widget class="QDialog" name="CustomPropertyEditorDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>389</width>
    <height>468</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string notr="true"/>
  </property>
  <property name="sizeGripEnabled">
   <bool>true</bool>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>6</number>
   </property>
   <property name="topMargin">
    <number>6</number>
   </property>
   <property name="rightMargin">
    <number>6</number>
   </property>
   <property name="bottomMargin">
    <number>6</number>
   </property>
   <item>
    <widget class="QtnCustomPropertyWidget" name="propertyWidget" native="true">
     <property name="focusPolicy">
      <enum>Qt::StrongFocus</enum>
     </property>
     <property name="contextMenuPolicy">
      <enum>Qt::CustomContextMenu</enum>
     </property>
     <property name="acceptDrops">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="focusPolicy">
      <enum>Qt::StrongFocus</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
  <action name="actionPropertyAdd">
   <property name="text">
    <string notr="true"/>
   </property>
   <property name="toolTip">
    <string notr="true"/>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+=</string>
   </property>
   <property name="shortcutContext">
    <enum>Qt::WindowShortcut</enum>
   </property>
  </action>
  <action name="actionPropertyDelete">
   <property name="text">
    <string>Delete</string>
   </property>
   <property name="shortcut">
    <string notr="true">Del</string>
   </property>
   <property name="shortcutContext">
    <enum>Qt::WindowShortcut</enum>
   </property>
  </action>
  <action name="actionPropertyDuplicate">
   <property name="text">
    <string>Duplicate...</string>
   </property>
   <property name="toolTip">
    <string notr="true">Duplicate</string>
   </property>
  </action>
  <action name="actionPropertyOptions">
   <property name="text">
    <string>Options...</string>
   </property>
   <property name="toolTip">
    <string notr="true">Options</string>
   </property>
   <property name="statusTip">
    <string/>
   </property>
   <property name="shortcut">
    <string notr="true">F2</string>
   </property>
   <property name="shortcutContext">
    <enum>Qt::WindowShortcut</enum>
   </property>
  </action>
  <action name="actionPropertyCut">
   <property name="text">
    <string>Cut</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+X</string>
   </property>
   <property name="shortcutContext">
    <enum>Qt::WindowShortcut</enum>
   </property>
  </action>
  <action name="actionPropertyCopy">
   <property name="text">
    <string>Copy</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+C</string>
   </property>
   <property name="shortcutContext">
    <enum>Qt::WindowShortcut</enum>
   </property>
  </action>
  <action name="actionPropertyPaste">
   <property name="text">
    <string>Paste</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+V</string>
   </property>
   <property name="shortcutContext">
    <enum>Qt::WindowShortcut</enum>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QtnCustomPropertyWidget</class>
   <extends>QWidget</extends>
   <header>QtnProperty/CustomPropertyWidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
