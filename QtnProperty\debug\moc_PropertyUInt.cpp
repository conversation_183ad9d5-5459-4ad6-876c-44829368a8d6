/****************************************************************************
** Meta object code from reading C++ file 'PropertyUInt.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../Core/PropertyUInt.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'PropertyUInt.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtnPropertyUIntBase_t {
    QByteArrayData data[1];
    char stringdata0[20];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyUIntBase_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyUIntBase_t qt_meta_stringdata_QtnPropertyUIntBase = {
    {
QT_MOC_LITERAL(0, 0, 19) // "QtnPropertyUIntBase"

    },
    "QtnPropertyUIntBase"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyUIntBase[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyUIntBase::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyUIntBase::staticMetaObject = {
    { &QtnNumericPropertyBase<QtnSinglePropertyBase<quint32>>::staticMetaObject, qt_meta_stringdata_QtnPropertyUIntBase.data,
      qt_meta_data_QtnPropertyUIntBase,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyUIntBase::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyUIntBase::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyUIntBase.stringdata0))
        return static_cast<void*>(this);
    return QtnNumericPropertyBase<QtnSinglePropertyBase<quint32>>::qt_metacast(_clname);
}

int QtnPropertyUIntBase::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnNumericPropertyBase<QtnSinglePropertyBase<quint32>>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyUIntCallback_t {
    QByteArrayData data[3];
    char stringdata0[32];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyUIntCallback_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyUIntCallback_t qt_meta_stringdata_QtnPropertyUIntCallback = {
    {
QT_MOC_LITERAL(0, 0, 23), // "QtnPropertyUIntCallback"
QT_MOC_LITERAL(1, 24, 0), // ""
QT_MOC_LITERAL(2, 25, 6) // "parent"

    },
    "QtnPropertyUIntCallback\0\0parent"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyUIntCallback[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       2,   18, // constructors
       0,       // flags
       0,       // signalCount

 // constructors: parameters
    0x80000000 | 1, QMetaType::QObjectStar,    2,
    0x80000000 | 1,

 // constructors: name, argc, parameters, tag, flags
       0,    1,   14,    1, 0x0e /* Public */,
       0,    0,   17,    1, 0x2e /* Public | MethodCloned */,

       0        // eod
};

void QtnPropertyUIntCallback::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::CreateInstance) {
        switch (_id) {
        case 0: { QtnPropertyUIntCallback *_r = new QtnPropertyUIntCallback((*reinterpret_cast< QObject*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        case 1: { QtnPropertyUIntCallback *_r = new QtnPropertyUIntCallback();
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        default: break;
        }
    }
    Q_UNUSED(_o);
}

const QMetaObject QtnPropertyUIntCallback::staticMetaObject = {
    { &QtnSinglePropertyCallback<QtnPropertyUIntBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyUIntCallback.data,
      qt_meta_data_QtnPropertyUIntCallback,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyUIntCallback::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyUIntCallback::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyUIntCallback.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyCallback<QtnPropertyUIntBase>::qt_metacast(_clname);
}

int QtnPropertyUIntCallback::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyCallback<QtnPropertyUIntBase>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyUInt_t {
    QByteArrayData data[3];
    char stringdata0[24];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyUInt_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyUInt_t qt_meta_stringdata_QtnPropertyUInt = {
    {
QT_MOC_LITERAL(0, 0, 15), // "QtnPropertyUInt"
QT_MOC_LITERAL(1, 16, 0), // ""
QT_MOC_LITERAL(2, 17, 6) // "parent"

    },
    "QtnPropertyUInt\0\0parent"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyUInt[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       2,   18, // constructors
       0,       // flags
       0,       // signalCount

 // constructors: parameters
    0x80000000 | 1, QMetaType::QObjectStar,    2,
    0x80000000 | 1,

 // constructors: name, argc, parameters, tag, flags
       0,    1,   14,    1, 0x0e /* Public */,
       0,    0,   17,    1, 0x2e /* Public | MethodCloned */,

       0        // eod
};

void QtnPropertyUInt::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::CreateInstance) {
        switch (_id) {
        case 0: { QtnPropertyUInt *_r = new QtnPropertyUInt((*reinterpret_cast< QObject*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        case 1: { QtnPropertyUInt *_r = new QtnPropertyUInt();
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        default: break;
        }
    }
    Q_UNUSED(_o);
}

const QMetaObject QtnPropertyUInt::staticMetaObject = {
    { &QtnNumericPropertyValue<QtnPropertyUIntBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyUInt.data,
      qt_meta_data_QtnPropertyUInt,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyUInt::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyUInt::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyUInt.stringdata0))
        return static_cast<void*>(this);
    return QtnNumericPropertyValue<QtnPropertyUIntBase>::qt_metacast(_clname);
}

int QtnPropertyUInt::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnNumericPropertyValue<QtnPropertyUIntBase>::qt_metacall(_c, _id, _a);
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
