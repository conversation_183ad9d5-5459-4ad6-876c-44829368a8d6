/****************************************************************************
** Meta object code from reading C++ file 'PropertySet.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../PropertySet.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'PropertySet.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtnPropertySet_t {
    QByteArrayData data[35];
    char stringdata0[470];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertySet_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertySet_t qt_meta_stringdata_QtnPropertySet = {
    {
QT_MOC_LITERAL(0, 0, 14), // "QtnPropertySet"
QT_MOC_LITERAL(1, 15, 18), // "hasChildProperties"
QT_MOC_LITERAL(2, 34, 0), // ""
QT_MOC_LITERAL(3, 35, 15), // "childProperties"
QT_MOC_LITERAL(4, 51, 19), // "findChildProperties"
QT_MOC_LITERAL(5, 71, 23), // "QList<QtnPropertyBase*>"
QT_MOC_LITERAL(6, 95, 4), // "name"
QT_MOC_LITERAL(7, 100, 20), // "Qt::FindChildOptions"
QT_MOC_LITERAL(8, 121, 7), // "options"
QT_MOC_LITERAL(9, 129, 2), // "re"
QT_MOC_LITERAL(10, 132, 17), // "findChildProperty"
QT_MOC_LITERAL(11, 150, 16), // "QtnPropertyBase*"
QT_MOC_LITERAL(12, 167, 13), // "QtnPropertyID"
QT_MOC_LITERAL(13, 181, 2), // "id"
QT_MOC_LITERAL(14, 184, 20), // "clearChildProperties"
QT_MOC_LITERAL(15, 205, 16), // "addChildProperty"
QT_MOC_LITERAL(16, 222, 13), // "childProperty"
QT_MOC_LITERAL(17, 236, 13), // "moveOwnership"
QT_MOC_LITERAL(18, 250, 5), // "index"
QT_MOC_LITERAL(19, 256, 19), // "removeChildProperty"
QT_MOC_LITERAL(20, 276, 9), // "createNew"
QT_MOC_LITERAL(21, 286, 15), // "QtnPropertySet*"
QT_MOC_LITERAL(22, 302, 12), // "parentForNew"
QT_MOC_LITERAL(23, 315, 10), // "createCopy"
QT_MOC_LITERAL(24, 326, 13), // "parentForCopy"
QT_MOC_LITERAL(25, 340, 10), // "copyValues"
QT_MOC_LITERAL(26, 351, 19), // "propertySetCopyFrom"
QT_MOC_LITERAL(27, 371, 16), // "QtnPropertyState"
QT_MOC_LITERAL(28, 388, 10), // "ignoreMask"
QT_MOC_LITERAL(29, 399, 8), // "fromJson"
QT_MOC_LITERAL(30, 408, 10), // "jsonObject"
QT_MOC_LITERAL(31, 419, 23), // "QtnPropertyChangeReason"
QT_MOC_LITERAL(32, 443, 6), // "reason"
QT_MOC_LITERAL(33, 450, 6), // "toJson"
QT_MOC_LITERAL(34, 457, 12) // "QJsonObject&"

    },
    "QtnPropertySet\0hasChildProperties\0\0"
    "childProperties\0findChildProperties\0"
    "QList<QtnPropertyBase*>\0name\0"
    "Qt::FindChildOptions\0options\0re\0"
    "findChildProperty\0QtnPropertyBase*\0"
    "QtnPropertyID\0id\0clearChildProperties\0"
    "addChildProperty\0childProperty\0"
    "moveOwnership\0index\0removeChildProperty\0"
    "createNew\0QtnPropertySet*\0parentForNew\0"
    "createCopy\0parentForCopy\0copyValues\0"
    "propertySetCopyFrom\0QtnPropertyState\0"
    "ignoreMask\0fromJson\0jsonObject\0"
    "QtnPropertyChangeReason\0reason\0toJson\0"
    "QJsonObject&"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertySet[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
      19,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,  109,    2, 0x0a /* Public */,
       3,    0,  110,    2, 0x0a /* Public */,
       4,    2,  111,    2, 0x0a /* Public */,
       4,    1,  116,    2, 0x2a /* Public | MethodCloned */,
       4,    2,  119,    2, 0x0a /* Public */,
       4,    1,  124,    2, 0x2a /* Public | MethodCloned */,
      10,    1,  127,    2, 0x0a /* Public */,
      14,    0,  130,    2, 0x0a /* Public */,
      15,    3,  131,    2, 0x0a /* Public */,
      15,    2,  138,    2, 0x2a /* Public | MethodCloned */,
      15,    1,  143,    2, 0x2a /* Public | MethodCloned */,
      19,    1,  146,    2, 0x0a /* Public */,
      20,    1,  149,    2, 0x0a /* Public */,
      23,    1,  152,    2, 0x0a /* Public */,
      25,    2,  155,    2, 0x0a /* Public */,
      25,    1,  160,    2, 0x2a /* Public | MethodCloned */,
      29,    2,  163,    2, 0x0a /* Public */,
      29,    1,  168,    2, 0x2a /* Public | MethodCloned */,
      33,    1,  171,    2, 0x0a /* Public */,

 // slots: parameters
    QMetaType::Bool,
    QMetaType::Void,
    0x80000000 | 5, QMetaType::QString, 0x80000000 | 7,    6,    8,
    0x80000000 | 5, QMetaType::QString,    6,
    0x80000000 | 5, QMetaType::QRegularExpression, 0x80000000 | 7,    9,    8,
    0x80000000 | 5, QMetaType::QRegularExpression,    9,
    0x80000000 | 11, 0x80000000 | 12,   13,
    QMetaType::Void,
    QMetaType::Bool, 0x80000000 | 11, QMetaType::Bool, QMetaType::Int,   16,   17,   18,
    QMetaType::Bool, 0x80000000 | 11, QMetaType::Bool,   16,   17,
    QMetaType::Bool, 0x80000000 | 11,   16,
    QMetaType::Bool, 0x80000000 | 11,   16,
    0x80000000 | 21, QMetaType::QObjectStar,   22,
    0x80000000 | 21, QMetaType::QObjectStar,   24,
    QMetaType::Bool, 0x80000000 | 21, 0x80000000 | 27,   26,   28,
    QMetaType::Bool, 0x80000000 | 21,   26,
    QMetaType::Bool, QMetaType::QJsonObject, 0x80000000 | 31,   30,   32,
    QMetaType::Bool, QMetaType::QJsonObject,   30,
    QMetaType::Bool, 0x80000000 | 34,   30,

       0        // eod
};

void QtnPropertySet::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        QtnPropertySet *_t = static_cast<QtnPropertySet *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: { bool _r = _t->hasChildProperties();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 1: _t->childProperties(); break;
        case 2: { QList<QtnPropertyBase*> _r = _t->findChildProperties((*reinterpret_cast< QString(*)>(_a[1])),(*reinterpret_cast< Qt::FindChildOptions(*)>(_a[2])));
            if (_a[0]) *reinterpret_cast< QList<QtnPropertyBase*>*>(_a[0]) = std::move(_r); }  break;
        case 3: { QList<QtnPropertyBase*> _r = _t->findChildProperties((*reinterpret_cast< QString(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QList<QtnPropertyBase*>*>(_a[0]) = std::move(_r); }  break;
        case 4: { QList<QtnPropertyBase*> _r = _t->findChildProperties((*reinterpret_cast< const QRegularExpression(*)>(_a[1])),(*reinterpret_cast< Qt::FindChildOptions(*)>(_a[2])));
            if (_a[0]) *reinterpret_cast< QList<QtnPropertyBase*>*>(_a[0]) = std::move(_r); }  break;
        case 5: { QList<QtnPropertyBase*> _r = _t->findChildProperties((*reinterpret_cast< const QRegularExpression(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QList<QtnPropertyBase*>*>(_a[0]) = std::move(_r); }  break;
        case 6: { QtnPropertyBase* _r = _t->findChildProperty((*reinterpret_cast< QtnPropertyID(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QtnPropertyBase**>(_a[0]) = std::move(_r); }  break;
        case 7: _t->clearChildProperties(); break;
        case 8: { bool _r = _t->addChildProperty((*reinterpret_cast< QtnPropertyBase*(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 9: { bool _r = _t->addChildProperty((*reinterpret_cast< QtnPropertyBase*(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 10: { bool _r = _t->addChildProperty((*reinterpret_cast< QtnPropertyBase*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 11: { bool _r = _t->removeChildProperty((*reinterpret_cast< QtnPropertyBase*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 12: { QtnPropertySet* _r = _t->createNew((*reinterpret_cast< QObject*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QtnPropertySet**>(_a[0]) = std::move(_r); }  break;
        case 13: { QtnPropertySet* _r = _t->createCopy((*reinterpret_cast< QObject*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QtnPropertySet**>(_a[0]) = std::move(_r); }  break;
        case 14: { bool _r = _t->copyValues((*reinterpret_cast< QtnPropertySet*(*)>(_a[1])),(*reinterpret_cast< QtnPropertyState(*)>(_a[2])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 15: { bool _r = _t->copyValues((*reinterpret_cast< QtnPropertySet*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 16: { bool _r = _t->fromJson((*reinterpret_cast< const QJsonObject(*)>(_a[1])),(*reinterpret_cast< QtnPropertyChangeReason(*)>(_a[2])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 17: { bool _r = _t->fromJson((*reinterpret_cast< const QJsonObject(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 18: { bool _r = _t->toJson((*reinterpret_cast< QJsonObject(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 6:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyID >(); break;
            }
            break;
        case 8:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyBase* >(); break;
            }
            break;
        case 9:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyBase* >(); break;
            }
            break;
        case 10:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyBase* >(); break;
            }
            break;
        case 11:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyBase* >(); break;
            }
            break;
        case 14:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertySet* >(); break;
            case 1:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyState >(); break;
            }
            break;
        case 15:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertySet* >(); break;
            }
            break;
        case 16:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 1:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QtnPropertyChangeReason >(); break;
            }
            break;
        }
    }
}

const QMetaObject QtnPropertySet::staticMetaObject = {
    { &QtnPropertyBase::staticMetaObject, qt_meta_stringdata_QtnPropertySet.data,
      qt_meta_data_QtnPropertySet,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertySet::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertySet::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertySet.stringdata0))
        return static_cast<void*>(this);
    return QtnPropertyBase::qt_metacast(_clname);
}

int QtnPropertySet::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnPropertyBase::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 19)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 19;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 19)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 19;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
