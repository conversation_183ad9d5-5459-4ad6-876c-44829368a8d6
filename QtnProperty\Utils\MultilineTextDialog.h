/*******************************************************************************
Copyright (c) 2015-2019 <PERSON> <<EMAIL>>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*******************************************************************************/

#pragma once

#include "QtnProperty/Config.h"

#include <QDialog>

namespace Ui
{
class MultilineTextDialog;
}

class QAbstractButton;

class QTN_IMPORT_EXPORT MultilineTextDialog : public QDialog
{
	Q_OBJECT

public:
	explicit MultilineTextDialog(QWidget *parent = nullptr);
	virtual ~MultilineTextDialog();

	void setReadOnly(bool value);

	void setText(const QString &text);
	QString getText() const;

private slots:
	void on_buttonBox_clicked(QAbstractButton *button);

private:
	Ui::MultilineTextDialog *ui;
};
