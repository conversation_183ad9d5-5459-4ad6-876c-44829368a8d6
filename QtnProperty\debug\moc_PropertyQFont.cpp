/****************************************************************************
** Meta object code from reading C++ file 'PropertyQFont.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../GUI/PropertyQFont.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'PropertyQFont.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtnPropertyQFontBase_t {
    QByteArrayData data[1];
    char stringdata0[21];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQFontBase_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQFontBase_t qt_meta_stringdata_QtnPropertyQFontBase = {
    {
QT_MOC_LITERAL(0, 0, 20) // "QtnPropertyQFontBase"

    },
    "QtnPropertyQFontBase"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQFontBase[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyQFontBase::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyQFontBase::staticMetaObject = {
    { &QtnSinglePropertyBase<QFont>::staticMetaObject, qt_meta_stringdata_QtnPropertyQFontBase.data,
      qt_meta_data_QtnPropertyQFontBase,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQFontBase::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQFontBase::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQFontBase.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyBase<QFont>::qt_metacast(_clname);
}

int QtnPropertyQFontBase::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyBase<QFont>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyQFontCallback_t {
    QByteArrayData data[1];
    char stringdata0[25];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQFontCallback_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQFontCallback_t qt_meta_stringdata_QtnPropertyQFontCallback = {
    {
QT_MOC_LITERAL(0, 0, 24) // "QtnPropertyQFontCallback"

    },
    "QtnPropertyQFontCallback"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQFontCallback[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyQFontCallback::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyQFontCallback::staticMetaObject = {
    { &QtnSinglePropertyCallback<QtnPropertyQFontBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyQFontCallback.data,
      qt_meta_data_QtnPropertyQFontCallback,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQFontCallback::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQFontCallback::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQFontCallback.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyCallback<QtnPropertyQFontBase>::qt_metacast(_clname);
}

int QtnPropertyQFontCallback::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyCallback<QtnPropertyQFontBase>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyQFont_t {
    QByteArrayData data[1];
    char stringdata0[17];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQFont_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQFont_t qt_meta_stringdata_QtnPropertyQFont = {
    {
QT_MOC_LITERAL(0, 0, 16) // "QtnPropertyQFont"

    },
    "QtnPropertyQFont"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQFont[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyQFont::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyQFont::staticMetaObject = {
    { &QtnSinglePropertyValue<QtnPropertyQFontBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyQFont.data,
      qt_meta_data_QtnPropertyQFont,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQFont::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQFont::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQFont.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyValue<QtnPropertyQFontBase>::qt_metacast(_clname);
}

int QtnPropertyQFont::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyValue<QtnPropertyQFontBase>::qt_metacall(_c, _id, _a);
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
