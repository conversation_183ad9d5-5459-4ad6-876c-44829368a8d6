/****************************************************************************
** Meta object code from reading C++ file 'CustomPropertyEditorDialog.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../CustomPropertyEditorDialog.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'CustomPropertyEditorDialog.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_CustomPropertyEditorDialog_t {
    QByteArrayData data[15];
    char stringdata0[292];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_CustomPropertyEditorDialog_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_CustomPropertyEditorDialog_t qt_meta_stringdata_CustomPropertyEditorDialog = {
    {
QT_MOC_LITERAL(0, 0, 26), // "CustomPropertyEditorDialog"
QT_MOC_LITERAL(1, 27, 5), // "apply"
QT_MOC_LITERAL(2, 33, 0), // ""
QT_MOC_LITERAL(3, 34, 4), // "data"
QT_MOC_LITERAL(4, 39, 23), // "onActivePropertyChanged"
QT_MOC_LITERAL(5, 63, 16), // "QtnPropertyBase*"
QT_MOC_LITERAL(6, 80, 14), // "activeProperty"
QT_MOC_LITERAL(7, 95, 20), // "on_buttonBox_clicked"
QT_MOC_LITERAL(8, 116, 16), // "QAbstractButton*"
QT_MOC_LITERAL(9, 133, 6), // "button"
QT_MOC_LITERAL(10, 140, 44), // "on_propertyWidget_customConte..."
QT_MOC_LITERAL(11, 185, 3), // "pos"
QT_MOC_LITERAL(12, 189, 30), // "on_actionPropertyAdd_triggered"
QT_MOC_LITERAL(13, 220, 36), // "on_actionPropertyDuplicate_tr..."
QT_MOC_LITERAL(14, 257, 34) // "on_actionPropertyOptions_trig..."

    },
    "CustomPropertyEditorDialog\0apply\0\0"
    "data\0onActivePropertyChanged\0"
    "QtnPropertyBase*\0activeProperty\0"
    "on_buttonBox_clicked\0QAbstractButton*\0"
    "button\0on_propertyWidget_customContextMenuRequested\0"
    "pos\0on_actionPropertyAdd_triggered\0"
    "on_actionPropertyDuplicate_triggered\0"
    "on_actionPropertyOptions_triggered"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_CustomPropertyEditorDialog[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       7,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   49,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       4,    1,   52,    2, 0x08 /* Private */,
       7,    1,   55,    2, 0x08 /* Private */,
      10,    1,   58,    2, 0x08 /* Private */,
      12,    0,   61,    2, 0x08 /* Private */,
      13,    0,   62,    2, 0x08 /* Private */,
      14,    0,   63,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QVariant,    3,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 5,    6,
    QMetaType::Void, 0x80000000 | 8,    9,
    QMetaType::Void, QMetaType::QPoint,   11,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void CustomPropertyEditorDialog::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        CustomPropertyEditorDialog *_t = static_cast<CustomPropertyEditorDialog *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->apply((*reinterpret_cast< const QVariant(*)>(_a[1]))); break;
        case 1: _t->onActivePropertyChanged((*reinterpret_cast< QtnPropertyBase*(*)>(_a[1]))); break;
        case 2: _t->on_buttonBox_clicked((*reinterpret_cast< QAbstractButton*(*)>(_a[1]))); break;
        case 3: _t->on_propertyWidget_customContextMenuRequested((*reinterpret_cast< const QPoint(*)>(_a[1]))); break;
        case 4: _t->on_actionPropertyAdd_triggered(); break;
        case 5: _t->on_actionPropertyDuplicate_triggered(); break;
        case 6: _t->on_actionPropertyOptions_triggered(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            typedef void (CustomPropertyEditorDialog::*_t)(const QVariant & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CustomPropertyEditorDialog::apply)) {
                *result = 0;
                return;
            }
        }
    }
}

const QMetaObject CustomPropertyEditorDialog::staticMetaObject = {
    { &QDialog::staticMetaObject, qt_meta_stringdata_CustomPropertyEditorDialog.data,
      qt_meta_data_CustomPropertyEditorDialog,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *CustomPropertyEditorDialog::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CustomPropertyEditorDialog::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CustomPropertyEditorDialog.stringdata0))
        return static_cast<void*>(this);
    return QDialog::qt_metacast(_clname);
}

int CustomPropertyEditorDialog::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QDialog::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 7;
    }
    return _id;
}

// SIGNAL 0
void CustomPropertyEditorDialog::apply(const QVariant & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
