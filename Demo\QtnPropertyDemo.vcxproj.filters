﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Form Files">
      <UniqueIdentifier>{99349809-55BA-4b9d-BF79-8FDBB0286EB3}</UniqueIdentifier>
      <Extensions>ui</Extensions>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="Form Files">
      <UniqueIdentifier>{99349809-55BA-4b9d-BF79-8FDBB0286EB3}</UniqueIdentifier>
      <Extensions>ui</Extensions>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="Generated Files">
      <UniqueIdentifier>{71ED8ED8-ACB9-4CE9-BBE1-E00B30144E11}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;moc;h;def;odl;idl;res;</Extensions>
    </Filter>
    <Filter Include="Generated Files">
      <UniqueIdentifier>{71ED8ED8-ACB9-4CE9-BBE1-E00B30144E11}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;moc;h;def;odl;idl;res;</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="PropertyEnum">
      <UniqueIdentifier>{E0D8C965-CC5F-43d7-AD63-FAEF0BBC0F85}-peg</UniqueIdentifier>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="PropertyEnum">
      <UniqueIdentifier>{E0D8C965-CC5F-43d7-AD63-FAEF0BBC0F85}-peg</UniqueIdentifier>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="my_moc_header">
      <UniqueIdentifier>{E0D8C965-CC5F-43d7-AD63-FAEF0BBC0F85}-peg_moc</UniqueIdentifier>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="my_moc_header">
      <UniqueIdentifier>{E0D8C965-CC5F-43d7-AD63-FAEF0BBC0F85}-peg_moc</UniqueIdentifier>
      <ParseFiles>false</ParseFiles>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="MainWindow.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AB\PropertyABColor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AB\PropertyDelegateABColor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Freq\PropertyDelegateFreq.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Int\PropertyDelegateIntList.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Layer\PropertyDelegateLayer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PenWidth\PropertyDelegatePenWidth.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Freq\PropertyFreq.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Layer\PropertyLayer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PenWidth\PropertyPenWidth.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="mydialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="MainWindow.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="AB\PropertyABColor.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <ClInclude Include="AB\PropertyDelegateABColor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Freq\PropertyDelegateFreq.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Int\PropertyDelegateIntList.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Layer\PropertyDelegateLayer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="PenWidth\PropertyDelegatePenWidth.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <QtMoc Include="Freq\PropertyFreq.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="Layer\PropertyLayer.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="PenWidth\PropertyPenWidth.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="mydialog.h">
      <Filter>Header Files</Filter>
    </QtMoc>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Demo.peg.cpp">
      <Filter>Generated Files</Filter>
    </ClCompile>
    <CustomBuild Include="moc_predefs.h.cbt">
      <Filter>Generated Files</Filter>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <QtUic Include="MainWindow.ui">
      <Filter>Form Files</Filter>
    </QtUic>
    <QtUic Include="mydialog.ui">
      <Filter>Form Files</Filter>
    </QtUic>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="Demo.pef">
      <Filter>PropertyEnum</Filter>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="Demo.peg.h">
      <Filter>my_moc_header</Filter>
    </QtMoc>
  </ItemGroup>
</Project>