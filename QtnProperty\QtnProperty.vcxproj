﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{AC33FEBA-8BB8-357B-BA3B-3E22DB0C1C1A}</ProjectGuid>
    <RootNamespace>QtnProperty</RootNamespace>
    <Keyword>QtVS_v304</Keyword>
    <WindowsTargetPlatformVersion>8.1</WindowsTargetPlatformVersion>
    <QtMsBuild Condition="'$(QtMsBuild)'=='' OR !Exists('$(QtMsBuild)\qt.targets')">$(MSBuildProjectDirectory)\QtMsBuild</QtMsBuild>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <PlatformToolset>v141</PlatformToolset>
    <OutputDirectory>..\bin-win32-msvc1944-x86_64\</OutputDirectory>
    <ATLMinimizesCRunTimeLibraryUsage>false</ATLMinimizesCRunTimeLibraryUsage>
    <CharacterSet>NotSet</CharacterSet>
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <IntermediateDirectory>release\</IntermediateDirectory>
    <PrimaryOutput>QtnProperty</PrimaryOutput>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <PlatformToolset>v140</PlatformToolset>
    <OutputDirectory>..\bin-win32-msvc1944-x86_64\debug\</OutputDirectory>
    <ATLMinimizesCRunTimeLibraryUsage>false</ATLMinimizesCRunTimeLibraryUsage>
    <CharacterSet>NotSet</CharacterSet>
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <IntermediateDirectory>debug\</IntermediateDirectory>
    <PrimaryOutput>QtnProperty</PrimaryOutput>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <Import Project="$(QtMsBuild)\qt_defaults.props" Condition="Exists('$(QtMsBuild)\qt_defaults.props')" />
  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <QtInstall>5.9.9_msvc2015_64</QtInstall>
    <QtModules>core;gui;script;widgets</QtModules>
  </PropertyGroup>
  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <QtInstall>5.9.9_msvc2015_64</QtInstall>
    <QtModules>core;gui;script;widgets</QtModules>
  </PropertyGroup>
  <Target Name="QtMsBuildNotFound" BeforeTargets="CustomBuild;ClCompile" Condition="!Exists('$(QtMsBuild)\qt.targets') OR !Exists('$(QtMsBuild)\Qt.props')">
    <Message Importance="High" Text="QtMsBuild: could not locate qt.targets, qt.props; project may not build correctly." />
  </Target>
  <ImportGroup Label="ExtensionSettings" />
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" />
    <Import Project="$(QtMsBuild)\Qt.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" />
    <Import Project="$(QtMsBuild)\Qt.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">..\bin-win32-msvc1944-x86_64\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">QtnProperty</TargetName>
    <IgnoreImportLibrary Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</IgnoreImportLibrary>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">..\bin-win32-msvc1944-x86_64\debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">QtnProperty</TargetName>
    <IgnoreImportLibrary Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</IgnoreImportLibrary>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;..\.;release;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>-Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 %(AdditionalOptions)</AdditionalOptions>
      <AssemblerListingLocation>release\</AssemblerListingLocation>
      <BrowseInformation>false</BrowseInformation>
      <DebugInformationFormat>None</DebugInformationFormat>
      <DisableSpecificWarnings>4577;4467;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ObjectFileName>release\</ObjectFileName>
      <Optimization>MaxSpeed</Optimization>
      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;NDEBUG;QT_NO_DEBUG;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessToFile>false</PreprocessToFile>
      <ProgramDataBaseFileName>
      </ProgramDataBaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <WarningLevel>Level3</WarningLevel>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <Lib>
      <OutputFile>$(OutDir)\QtnProperty.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
    <Midl>
      <DefaultCharType>Unsigned</DefaultCharType>
      <EnableErrorChecks>None</EnableErrorChecks>
      <WarningLevel>0</WarningLevel>
    </Midl>
    <ResourceCompile>
      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;NDEBUG;QT_NO_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <QtMoc>
      <CompilerFlavor>msvc</CompilerFlavor>
      <Include>$(Configuration)/moc_predefs.h</Include>
      <ExecutionDescription>Moc'ing %(Identity)...</ExecutionDescription>
      <DynamicSource>output</DynamicSource>
      <QtMocDir>$(Configuration)</QtMocDir>
      <QtMocFileName>moc_%(Filename).cpp</QtMocFileName>
    </QtMoc>
    <QtRcc>
      <Compression>default</Compression>
      <InitFuncName>QtnProperty</InitFuncName>
      <ExecutionDescription>Rcc'ing %(Identity)...</ExecutionDescription>
      <QtRccDir>$(Configuration)</QtRccDir>
      <QtRccFileName>qrc_%(Filename).cpp</QtRccFileName>
    </QtRcc>
    <QtUic>
      <ExecutionDescription>Uic'ing %(Identity)...</ExecutionDescription>
      <QtUicDir>$(ProjectDir)</QtUicDir>
      <QtUicFileName>ui_%(Filename).h</QtUicFileName>
    </QtUic>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;..\.;debug;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>-Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 %(AdditionalOptions)</AdditionalOptions>
      <AssemblerListingLocation>debug\</AssemblerListingLocation>
      <BrowseInformation>false</BrowseInformation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4577;4467;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ObjectFileName>debug\</ObjectFileName>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessToFile>false</PreprocessToFile>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <WarningLevel>Level3</WarningLevel>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <Lib>
      <OutputFile>$(OutDir)\QtnProperty.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
    <Midl>
      <DefaultCharType>Unsigned</DefaultCharType>
      <EnableErrorChecks>None</EnableErrorChecks>
      <WarningLevel>0</WarningLevel>
    </Midl>
    <ResourceCompile>
      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;DEBUG;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <QtRcc>
      <Compression>default</Compression>
      <InitFuncName>QtnProperty</InitFuncName>
      <ExecutionDescription>Rcc'ing %(Identity)...</ExecutionDescription>
      <QtRccDir>$(Configuration)</QtRccDir>
      <QtRccFileName>qrc_%(Filename).cpp</QtRccFileName>
    </QtRcc>
    <QtMoc>
      <CompilerFlavor>msvc</CompilerFlavor>
      <Include>$(Configuration)/moc_predefs.h</Include>
      <ExecutionDescription>Moc'ing %(Identity)...</ExecutionDescription>
      <DynamicSource>output</DynamicSource>
      <QtMocDir>$(Configuration)</QtMocDir>
      <QtMocFileName>moc_%(Filename).cpp</QtMocFileName>
    </QtMoc>
    <QtUic>
      <ExecutionDescription>Uic'ing %(Identity)...</ExecutionDescription>
      <QtUicDir>$(ProjectDir)</QtUicDir>
      <QtUicFileName>ui_%(Filename).h</QtUicFileName>
    </QtUic>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="Utils\AccessibilityProxy.cpp" />
    <ClCompile Include="CustomPropertyEditorDialog.cpp" />
    <ClCompile Include="CustomPropertyOptionsDialog.cpp" />
    <ClCompile Include="CustomPropertyWidget.cpp" />
    <ClCompile Include="Utils\DoubleSpinBox.cpp" />
    <ClCompile Include="Enum.cpp" />
    <ClCompile Include="Utils\InplaceEditing.cpp" />
    <ClCompile Include="Install.cpp" />
    <ClCompile Include="MultiProperty.cpp" />
    <ClCompile Include="Utils\MultilineTextDialog.cpp" />
    <ClCompile Include="Property.cpp" />
    <ClCompile Include="PropertyBase.cpp" />
    <ClCompile Include="Core\PropertyBool.cpp" />
    <ClCompile Include="GUI\PropertyButton.cpp" />
    <ClCompile Include="PropertyConnector.cpp" />
    <ClCompile Include="Delegates\PropertyDelegate.cpp" />
    <ClCompile Include="Delegates\PropertyDelegateAux.cpp" />
    <ClCompile Include="Delegates\Core\PropertyDelegateBool.cpp" />
    <ClCompile Include="Delegates\GUI\PropertyDelegateButton.cpp" />
    <ClCompile Include="Delegates\Core\PropertyDelegateDouble.cpp" />
    <ClCompile Include="Delegates\Core\PropertyDelegateEnum.cpp" />
    <ClCompile Include="Delegates\Core\PropertyDelegateEnumFlags.cpp" />
    <ClCompile Include="Delegates\PropertyDelegateFactory.cpp" />
    <ClCompile Include="Delegates\Core\PropertyDelegateFloat.cpp" />
    <ClCompile Include="Delegates\Utils\PropertyDelegateGeoCoord.cpp" />
    <ClCompile Include="Delegates\Utils\PropertyDelegateGeoPoint.cpp" />
    <ClCompile Include="Auxiliary\PropertyDelegateInfo.cpp" />
    <ClCompile Include="Delegates\Core\PropertyDelegateInt.cpp" />
    <ClCompile Include="PropertyDelegateMetaEnum.cpp" />
    <ClCompile Include="Delegates\Utils\PropertyDelegateMisc.cpp" />
    <ClCompile Include="Delegates\Utils\PropertyDelegatePropertySet.cpp" />
    <ClCompile Include="Delegates\GUI\PropertyDelegateQBrush.cpp" />
    <ClCompile Include="Delegates\GUI\PropertyDelegateQColor.cpp" />
    <ClCompile Include="Delegates\GUI\PropertyDelegateQFont.cpp" />
    <ClCompile Include="Delegates\GUI\PropertyDelegateQPen.cpp" />
    <ClCompile Include="Delegates\Core\PropertyDelegateQPoint.cpp" />
    <ClCompile Include="Delegates\Core\PropertyDelegateQPointF.cpp" />
    <ClCompile Include="Delegates\Core\PropertyDelegateQRect.cpp" />
    <ClCompile Include="Delegates\Core\PropertyDelegateQRectF.cpp" />
    <ClCompile Include="Delegates\Core\PropertyDelegateQSize.cpp" />
    <ClCompile Include="Delegates\Core\PropertyDelegateQSizeF.cpp" />
    <ClCompile Include="Delegates\Core\PropertyDelegateQString.cpp" />
    <ClCompile Include="Delegates\GUI\PropertyDelegateQVector3D.cpp" />
    <ClCompile Include="Delegates\Utils\PropertyDelegateSliderBox.cpp" />
    <ClCompile Include="Delegates\Core\PropertyDelegateUInt.cpp" />
    <ClCompile Include="Core\PropertyDouble.cpp" />
    <ClCompile Include="Delegates\Utils\PropertyEditorAux.cpp" />
    <ClCompile Include="Delegates\Utils\PropertyEditorHandler.cpp" />
    <ClCompile Include="Core\PropertyEnum.cpp" />
    <ClCompile Include="Core\PropertyEnumFlags.cpp" />
    <ClCompile Include="Core\PropertyFloat.cpp" />
    <ClCompile Include="Core\PropertyInt.cpp" />
    <ClCompile Include="PropertyInt64.cpp" />
    <ClCompile Include="GUI\PropertyQBrush.cpp" />
    <ClCompile Include="GUI\PropertyQColor.cpp" />
    <ClCompile Include="GUI\PropertyQFont.cpp" />
    <ClCompile Include="PropertyQKeySequence.cpp" />
    <ClCompile Include="GUI\PropertyQPen.cpp" />
    <ClCompile Include="Core\PropertyQPoint.cpp" />
    <ClCompile Include="Core\PropertyQPointF.cpp" />
    <ClCompile Include="Core\PropertyQRect.cpp" />
    <ClCompile Include="Core\PropertyQRectF.cpp" />
    <ClCompile Include="Core\PropertyQSize.cpp" />
    <ClCompile Include="Core\PropertyQSizeF.cpp" />
    <ClCompile Include="Core\PropertyQString.cpp" />
    <ClCompile Include="PropertyQVariant.cpp" />
    <ClCompile Include="GUI\PropertyQVector3D.cpp" />
    <ClCompile Include="PropertySet.cpp" />
    <ClCompile Include="Core\PropertyUInt.cpp" />
    <ClCompile Include="PropertyUInt64.cpp" />
    <ClCompile Include="PropertyView.cpp" />
    <ClCompile Include="PropertyWidget.cpp" />
    <ClCompile Include="PropertyWidgetEx.cpp" />
    <ClCompile Include="QObjectPropertySet.cpp" />
    <ClCompile Include="QObjectPropertyWidget.cpp" />
    <ClCompile Include="Utils\QtnCompleterItemDelegate.cpp" />
    <ClCompile Include="Utils\QtnCompleterLineEdit.cpp" />
    <ClCompile Include="Utils\QtnConnections.cpp" />
    <ClCompile Include="Utils\QtnInt64SpinBox.cpp" />
    <ClCompile Include="VarProperty.cpp" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="Utils\AccessibilityProxy.h" />
    <ClInclude Include="Config.h" />
    <QtMoc Include="CustomPropertyEditorDialog.h" />
    <QtMoc Include="CustomPropertyOptionsDialog.h" />
    <QtMoc Include="CustomPropertyWidget.h" />
    <ClInclude Include="Utils\DoubleSpinBox.h" />
    <ClInclude Include="Enum.h" />
    <ClInclude Include="FunctionalHelpers.h" />
    <ClInclude Include="IQtnPropertyStateProvider.h" />
    <ClInclude Include="Utils\InplaceEditing.h" />
    <ClInclude Include="Install.h" />
    <QtMoc Include="MultiProperty.h" />
    <QtMoc Include="Utils\MultilineTextDialog.h" />
    <QtMoc Include="Property.h" />
    <ClInclude Include="Auxiliary\PropertyAux.h" />
    <QtMoc Include="PropertyBase.h" />
    <QtMoc Include="Core\PropertyBool.h" />
    <QtMoc Include="GUI\PropertyButton.h" />
    <QtMoc Include="PropertyConnector.h" />
    <ClInclude Include="PropertyCore.h" />
    <ClInclude Include="Delegates\PropertyDelegate.h" />
    <ClInclude Include="PropertyDelegateAttrs.h" />
    <ClInclude Include="Delegates\PropertyDelegateAux.h" />
    <ClInclude Include="Delegates\Core\PropertyDelegateBool.h" />
    <ClInclude Include="Delegates\GUI\PropertyDelegateButton.h" />
    <ClInclude Include="Delegates\Core\PropertyDelegateDouble.h" />
    <ClInclude Include="Delegates\Core\PropertyDelegateEnum.h" />
    <ClInclude Include="Delegates\Core\PropertyDelegateEnumFlags.h" />
    <ClInclude Include="Delegates\PropertyDelegateFactory.h" />
    <ClInclude Include="Delegates\Core\PropertyDelegateFloat.h" />
    <ClInclude Include="Delegates\Utils\PropertyDelegateGeoCoord.h" />
    <ClInclude Include="Delegates\Utils\PropertyDelegateGeoPoint.h" />
    <ClInclude Include="Auxiliary\PropertyDelegateInfo.h" />
    <ClInclude Include="Delegates\Core\PropertyDelegateInt.h" />
    <ClInclude Include="PropertyDelegateMetaEnum.h" />
    <ClInclude Include="Delegates\Utils\PropertyDelegateMisc.h" />
    <ClInclude Include="Delegates\Utils\PropertyDelegatePropertySet.h" />
    <ClInclude Include="Delegates\GUI\PropertyDelegateQBrush.h" />
    <ClInclude Include="Delegates\GUI\PropertyDelegateQColor.h" />
    <ClInclude Include="Delegates\GUI\PropertyDelegateQFont.h" />
    <ClInclude Include="Delegates\GUI\PropertyDelegateQPen.h" />
    <ClInclude Include="Delegates\Core\PropertyDelegateQPoint.h" />
    <ClInclude Include="Delegates\Core\PropertyDelegateQPointF.h" />
    <ClInclude Include="Delegates\Core\PropertyDelegateQRect.h" />
    <ClInclude Include="Delegates\Core\PropertyDelegateQRectF.h" />
    <ClInclude Include="Delegates\Core\PropertyDelegateQSize.h" />
    <ClInclude Include="Delegates\Core\PropertyDelegateQSizeF.h" />
    <ClInclude Include="Delegates\Core\PropertyDelegateQString.h" />
    <ClInclude Include="Delegates\GUI\PropertyDelegateQVector3D.h" />
    <ClInclude Include="Delegates\Utils\PropertyDelegateSliderBox.h" />
    <ClInclude Include="Delegates\Core\PropertyDelegateUInt.h" />
    <QtMoc Include="Core\PropertyDouble.h" />
    <ClInclude Include="Delegates\Utils\PropertyEditorAux.h" />
    <ClInclude Include="Delegates\Utils\PropertyEditorHandler.h" />
    <QtMoc Include="Core\PropertyEnum.h" />
    <QtMoc Include="Core\PropertyEnumFlags.h" />
    <QtMoc Include="Core\PropertyFloat.h" />
    <ClInclude Include="PropertyGUI.h" />
    <QtMoc Include="Core\PropertyInt.h" />
    <QtMoc Include="PropertyInt64.h" />
    <ClInclude Include="Auxiliary\PropertyMacro.h" />
    <QtMoc Include="GUI\PropertyQBrush.h" />
    <QtMoc Include="GUI\PropertyQColor.h" />
    <QtMoc Include="GUI\PropertyQFont.h" />
    <QtMoc Include="PropertyQKeySequence.h" />
    <QtMoc Include="GUI\PropertyQPen.h" />
    <QtMoc Include="Core\PropertyQPoint.h" />
    <QtMoc Include="Core\PropertyQPointF.h" />
    <QtMoc Include="Core\PropertyQRect.h" />
    <QtMoc Include="Core\PropertyQRectF.h" />
    <QtMoc Include="Core\PropertyQSize.h" />
    <QtMoc Include="Core\PropertyQSizeF.h" />
    <QtMoc Include="Core\PropertyQString.h" />
    <QtMoc Include="PropertyQVariant.h" />
    <QtMoc Include="GUI\PropertyQVector3D.h" />
    <QtMoc Include="PropertySet.h" />
    <ClInclude Include="Auxiliary\PropertyTemplates.h" />
    <QtMoc Include="Core\PropertyUInt.h" />
    <QtMoc Include="PropertyUInt64.h" />
    <QtMoc Include="PropertyView.h" />
    <QtMoc Include="PropertyWidget.h" />
    <QtMoc Include="PropertyWidgetEx.h" />
    <ClInclude Include="QObjectPropertySet.h" />
    <QtMoc Include="QObjectPropertyWidget.h" />
    <QtMoc Include="Utils\QtnCompleterItemDelegate.h" />
    <QtMoc Include="Utils\QtnCompleterLineEdit.h" />
    <ClInclude Include="Utils\QtnConnections.h" />
    <QtMoc Include="Utils\QtnInt64SpinBox.h" />
    <ClInclude Include="StructPropertyBase.h" />
    <QtMoc Include="VarProperty.h" />
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="debug\moc_predefs.h.cbt">
      <FileType>Document</FileType>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\mkspecs\features\data\dummy.cpp;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -Zi -MDd -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E $(QTDIR)\mkspecs\features\data\dummy.cpp 2&gt;NUL &gt;$(IntDir)\moc_predefs.h</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Generate moc_predefs.h</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(IntDir)\moc_predefs.h;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="release\moc_predefs.h.cbt">
      <FileType>Document</FileType>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\mkspecs\features\data\dummy.cpp;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -O2 -MD -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E $(QTDIR)\mkspecs\features\data\dummy.cpp 2&gt;NUL &gt;$(IntDir)\moc_predefs.h</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Generate moc_predefs.h</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(IntDir)\moc_predefs.h;%(Outputs)</Outputs>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include=".\Translations\en.ts" />
    <None Include=".\Translations\ru.ts" />
  </ItemGroup>
  <ItemGroup>
    <QtUic Include="CustomPropertyEditorDialog.ui" />
    <QtUic Include="CustomPropertyOptionsDialog.ui" />
    <QtUic Include="Utils\MultilineTextDialog.ui" />
  </ItemGroup>
  <ItemGroup>
    <QtRcc Include="QtnProperty.qrc" />
    <None Include="Translations\en.qm" />
    <None Include="Translations\ru.qm" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <Import Project="$(QtMsBuild)\qt.targets" Condition="Exists('$(QtMsBuild)\qt.targets')" />
  <ImportGroup Label="ExtensionTargets" />
</Project>