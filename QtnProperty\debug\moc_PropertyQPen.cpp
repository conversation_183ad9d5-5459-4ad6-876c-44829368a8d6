/****************************************************************************
** Meta object code from reading C++ file 'PropertyQPen.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../GUI/PropertyQPen.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'PropertyQPen.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtnPropertyQPenStyleBase_t {
    QByteArrayData data[1];
    char stringdata0[25];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQPenStyleBase_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQPenStyleBase_t qt_meta_stringdata_QtnPropertyQPenStyleBase = {
    {
QT_MOC_LITERAL(0, 0, 24) // "QtnPropertyQPenStyleBase"

    },
    "QtnPropertyQPenStyleBase"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQPenStyleBase[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyQPenStyleBase::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyQPenStyleBase::staticMetaObject = {
    { &QtnSinglePropertyBase<Qt::PenStyle>::staticMetaObject, qt_meta_stringdata_QtnPropertyQPenStyleBase.data,
      qt_meta_data_QtnPropertyQPenStyleBase,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQPenStyleBase::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQPenStyleBase::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQPenStyleBase.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyBase<Qt::PenStyle>::qt_metacast(_clname);
}

int QtnPropertyQPenStyleBase::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyBase<Qt::PenStyle>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyQPenStyleCallback_t {
    QByteArrayData data[1];
    char stringdata0[29];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQPenStyleCallback_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQPenStyleCallback_t qt_meta_stringdata_QtnPropertyQPenStyleCallback = {
    {
QT_MOC_LITERAL(0, 0, 28) // "QtnPropertyQPenStyleCallback"

    },
    "QtnPropertyQPenStyleCallback"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQPenStyleCallback[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyQPenStyleCallback::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyQPenStyleCallback::staticMetaObject = {
    { &QtnSinglePropertyCallback<QtnPropertyQPenStyleBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyQPenStyleCallback.data,
      qt_meta_data_QtnPropertyQPenStyleCallback,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQPenStyleCallback::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQPenStyleCallback::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQPenStyleCallback.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyCallback<QtnPropertyQPenStyleBase>::qt_metacast(_clname);
}

int QtnPropertyQPenStyleCallback::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyCallback<QtnPropertyQPenStyleBase>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyQPenStyle_t {
    QByteArrayData data[1];
    char stringdata0[21];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQPenStyle_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQPenStyle_t qt_meta_stringdata_QtnPropertyQPenStyle = {
    {
QT_MOC_LITERAL(0, 0, 20) // "QtnPropertyQPenStyle"

    },
    "QtnPropertyQPenStyle"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQPenStyle[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyQPenStyle::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyQPenStyle::staticMetaObject = {
    { &QtnSinglePropertyValue<QtnPropertyQPenStyleBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyQPenStyle.data,
      qt_meta_data_QtnPropertyQPenStyle,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQPenStyle::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQPenStyle::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQPenStyle.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyValue<QtnPropertyQPenStyleBase>::qt_metacast(_clname);
}

int QtnPropertyQPenStyle::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyValue<QtnPropertyQPenStyleBase>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyQPenBase_t {
    QByteArrayData data[1];
    char stringdata0[20];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQPenBase_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQPenBase_t qt_meta_stringdata_QtnPropertyQPenBase = {
    {
QT_MOC_LITERAL(0, 0, 19) // "QtnPropertyQPenBase"

    },
    "QtnPropertyQPenBase"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQPenBase[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyQPenBase::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyQPenBase::staticMetaObject = {
    { &QtnSinglePropertyBase<QPen>::staticMetaObject, qt_meta_stringdata_QtnPropertyQPenBase.data,
      qt_meta_data_QtnPropertyQPenBase,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQPenBase::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQPenBase::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQPenBase.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyBase<QPen>::qt_metacast(_clname);
}

int QtnPropertyQPenBase::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyBase<QPen>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyQPenCallback_t {
    QByteArrayData data[1];
    char stringdata0[24];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQPenCallback_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQPenCallback_t qt_meta_stringdata_QtnPropertyQPenCallback = {
    {
QT_MOC_LITERAL(0, 0, 23) // "QtnPropertyQPenCallback"

    },
    "QtnPropertyQPenCallback"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQPenCallback[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyQPenCallback::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyQPenCallback::staticMetaObject = {
    { &QtnSinglePropertyCallback<QtnPropertyQPenBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyQPenCallback.data,
      qt_meta_data_QtnPropertyQPenCallback,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQPenCallback::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQPenCallback::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQPenCallback.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyCallback<QtnPropertyQPenBase>::qt_metacast(_clname);
}

int QtnPropertyQPenCallback::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyCallback<QtnPropertyQPenBase>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyQPen_t {
    QByteArrayData data[1];
    char stringdata0[16];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyQPen_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyQPen_t qt_meta_stringdata_QtnPropertyQPen = {
    {
QT_MOC_LITERAL(0, 0, 15) // "QtnPropertyQPen"

    },
    "QtnPropertyQPen"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyQPen[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyQPen::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyQPen::staticMetaObject = {
    { &QtnSinglePropertyValue<QtnPropertyQPenBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyQPen.data,
      qt_meta_data_QtnPropertyQPen,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyQPen::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyQPen::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyQPen.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyValue<QtnPropertyQPenBase>::qt_metacast(_clname);
}

int QtnPropertyQPen::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyValue<QtnPropertyQPenBase>::qt_metacall(_c, _id, _a);
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
