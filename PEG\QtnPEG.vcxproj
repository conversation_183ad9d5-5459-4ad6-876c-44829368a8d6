﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{E667DFE0-E36E-340E-B120-A2A79E1109DD}</ProjectGuid>
    <RootNamespace>QtnPEG</RootNamespace>
    <Keyword>QtVS_v304</Keyword>
    <WindowsTargetPlatformVersion>8.1</WindowsTargetPlatformVersion>
    <QtMsBuild Condition="'$(QtMsBuild)'=='' OR !Exists('$(QtMsBuild)\qt.targets')">$(MSBuildProjectDirectory)\QtMsBuild</QtMsBuild>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <PlatformToolset>v141</PlatformToolset>
    <OutputDirectory>..\bin-win32-msvc1944-x86_64\</OutputDirectory>
    <ATLMinimizesCRunTimeLibraryUsage>false</ATLMinimizesCRunTimeLibraryUsage>
    <CharacterSet>NotSet</CharacterSet>
    <ConfigurationType>Application</ConfigurationType>
    <IntermediateDirectory>release\</IntermediateDirectory>
    <PrimaryOutput>QtnPEG</PrimaryOutput>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <PlatformToolset>v140</PlatformToolset>
    <OutputDirectory>..\bin-win32-msvc1944-x86_64\debug\</OutputDirectory>
    <ATLMinimizesCRunTimeLibraryUsage>false</ATLMinimizesCRunTimeLibraryUsage>
    <CharacterSet>NotSet</CharacterSet>
    <ConfigurationType>Application</ConfigurationType>
    <IntermediateDirectory>debug\</IntermediateDirectory>
    <PrimaryOutput>QtnPEG</PrimaryOutput>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <Import Project="$(QtMsBuild)\qt_defaults.props" Condition="Exists('$(QtMsBuild)\qt_defaults.props')" />
  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <QtInstall>5.9.9_msvc2015_64</QtInstall>
    <QtModules>core</QtModules>
  </PropertyGroup>
  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <QtInstall>5.9.9_msvc2015_64</QtInstall>
    <QtModules>core</QtModules>
  </PropertyGroup>
  <Target Name="QtMsBuildNotFound" BeforeTargets="CustomBuild;ClCompile" Condition="!Exists('$(QtMsBuild)\qt.targets') OR !Exists('$(QtMsBuild)\Qt.props')">
    <Message Importance="High" Text="QtMsBuild: could not locate qt.targets, qt.props; project may not build correctly." />
  </Target>
  <ImportGroup Label="ExtensionSettings" />
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" />
    <Import Project="$(QtMsBuild)\Qt.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" />
    <Import Project="$(QtMsBuild)\Qt.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">..\bin-win32-msvc1944-x86_64\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">QtnPEG</TargetName>
    <IgnoreImportLibrary Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</IgnoreImportLibrary>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">..\bin-win32-msvc1944-x86_64\debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">QtnPEG</TargetName>
    <IgnoreImportLibrary Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</IgnoreImportLibrary>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;release;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>-Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 %(AdditionalOptions)</AdditionalOptions>
      <AssemblerListingLocation>release\</AssemblerListingLocation>
      <BrowseInformation>false</BrowseInformation>
      <DebugInformationFormat>None</DebugInformationFormat>
      <DisableSpecificWarnings>4065;4267;4005;4577;4467;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ObjectFileName>release\</ObjectFileName>
      <Optimization>MaxSpeed</Optimization>
      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;NDEBUG;QT_NO_DEBUG;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessToFile>false</PreprocessToFile>
      <ProgramDataBaseFileName>
      </ProgramDataBaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <WarningLevel>Level3</WarningLevel>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <Link>
      <AdditionalDependencies>shell32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>C:\openssl\lib;C:\Utils\my_sql\my_sql\lib;C:\Utils\postgresql\pgsql\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>"/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='*******' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'" %(AdditionalOptions)</AdditionalOptions>
      <DataExecutionPrevention>true</DataExecutionPrevention>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreImportLibrary>true</IgnoreImportLibrary>
      <LinkIncremental>false</LinkIncremental>
      <OutputFile>$(OutDir)\QtnPEG.exe</OutputFile>
      <RandomizedBaseAddress>true</RandomizedBaseAddress>
      <SubSystem>Windows</SubSystem>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <Version>2.0</Version>
    </Link>
    <Midl>
      <DefaultCharType>Unsigned</DefaultCharType>
      <EnableErrorChecks>None</EnableErrorChecks>
      <WarningLevel>0</WarningLevel>
    </Midl>
    <ResourceCompile>
      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;NDEBUG;QT_NO_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <QtMoc>
      <CompilerFlavor>msvc</CompilerFlavor>
      <Include>$(Configuration)/moc_predefs.h</Include>
      <ExecutionDescription>Moc'ing %(Identity)...</ExecutionDescription>
      <DynamicSource>output</DynamicSource>
      <QtMocDir>$(Configuration)</QtMocDir>
      <QtMocFileName>moc_%(Filename).cpp</QtMocFileName>
    </QtMoc>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;debug;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>-Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 %(AdditionalOptions)</AdditionalOptions>
      <AssemblerListingLocation>debug\</AssemblerListingLocation>
      <BrowseInformation>false</BrowseInformation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4065;4267;4005;4577;4467;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ObjectFileName>debug\</ObjectFileName>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessToFile>false</PreprocessToFile>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <WarningLevel>Level3</WarningLevel>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <Link>
      <AdditionalDependencies>shell32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>C:\openssl\lib;C:\Utils\my_sql\my_sql\lib;C:\Utils\postgresql\pgsql\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>"/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='*******' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'" %(AdditionalOptions)</AdditionalOptions>
      <DataExecutionPrevention>true</DataExecutionPrevention>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreImportLibrary>true</IgnoreImportLibrary>
      <OutputFile>$(OutDir)\QtnPEG.exe</OutputFile>
      <RandomizedBaseAddress>true</RandomizedBaseAddress>
      <SubSystem>Windows</SubSystem>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <Version>2.0</Version>
    </Link>
    <Midl>
      <DefaultCharType>Unsigned</DefaultCharType>
      <EnableErrorChecks>None</EnableErrorChecks>
      <WarningLevel>0</WarningLevel>
    </Midl>
    <ResourceCompile>
      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;WIN64;_CRT_SECURE_NO_WARNINGS;DEBUG;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <QtMoc>
      <CompilerFlavor>msvc</CompilerFlavor>
      <Include>$(Configuration)/moc_predefs.h</Include>
      <ExecutionDescription>Moc'ing %(Identity)...</ExecutionDescription>
      <DynamicSource>output</DynamicSource>
      <QtMocDir>$(Configuration)</QtMocDir>
      <QtMocFileName>moc_%(Filename).cpp</QtMocFileName>
    </QtMoc>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="PropertyEnumGenerator.cpp" />
    <ClCompile Include="main.cpp" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="PropertyEnumGenerator.h" />
    <ClInclude Include="PropertyEnumGeneratorCommon.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="PropertyEnum.lexer.cpp" />
    <ClCompile Include="PropertyEnum.parser.cpp" />
    <CustomBuild Include="debug\moc_predefs.h.cbt">
      <FileType>Document</FileType>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\mkspecs\features\data\dummy.cpp;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding /wd4065 /wd4267 /wd4005 -Zi -MDd -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E $(QTDIR)\mkspecs\features\data\dummy.cpp 2&gt;NUL &gt;$(IntDir)\moc_predefs.h</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Generate moc_predefs.h</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(IntDir)\moc_predefs.h;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="release\moc_predefs.h.cbt">
      <FileType>Document</FileType>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\mkspecs\features\data\dummy.cpp;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding /wd4065 /wd4267 /wd4005 -O2 -MD -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E $(QTDIR)\mkspecs\features\data\dummy.cpp 2&gt;NUL &gt;$(IntDir)\moc_predefs.h</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Generate moc_predefs.h</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(IntDir)\moc_predefs.h;%(Outputs)</Outputs>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="PropertyEnum.y">
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">PropertyEnum.y;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">win_bison -d -o ./PropertyEnum.parser.cpp PropertyEnum.y</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Bison PropertyEnum.y</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">PropertyEnum.parser.cpp;%(Outputs)</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">PropertyEnum.y;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">win_bison -d -o ./PropertyEnum.parser.cpp PropertyEnum.y</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Bison PropertyEnum.y</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">PropertyEnum.parser.cpp;%(Outputs)</Outputs>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="PropertyEnum.l">
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">PropertyEnum.l;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">win_flex --wincompat -o ./PropertyEnum.lexer.cpp PropertyEnum.l</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Flex PropertyEnum.l</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">PropertyEnum.lexer.cpp;%(Outputs)</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">PropertyEnum.l;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">win_flex --wincompat -o ./PropertyEnum.lexer.cpp PropertyEnum.l</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Flex PropertyEnum.l</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">PropertyEnum.lexer.cpp;%(Outputs)</Outputs>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include=".\QtnPEG_resource.rc" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <Import Project="$(QtMsBuild)\qt.targets" Condition="Exists('$(QtMsBuild)\qt.targets')" />
  <ImportGroup Label="ExtensionTargets" />
</Project>