/*******************************************************************************
Copyright (c) 2012-2016 <PERSON> <<EMAIL>>
Copyright (c) 2015-2019 <PERSON> <<EMAIL>>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*******************************************************************************/

#pragma once

#include <qglobal.h>

#if defined(QTN_DYNAMIC_LIBRARY)
#define QTN_IMPORT_EXPORT Q_DECL_EXPORT

#elif defined(QTN_DYNAMIC_IMPORT)
#define QTN_IMPORT_EXPORT Q_DECL_IMPORT
#else

#define QTN_IMPORT_EXPORT
#ifndef QTN_STATIC_LIBRARY
#define QTN_STATIC_LIBRARY
#endif
#endif

#define PERCENT_SUFFIX 1
#define DEGREE_SUFFIX 2
