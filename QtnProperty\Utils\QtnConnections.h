/*******************************************************************************
Copyright (c) 2015-2019 <PERSON> <<EMAIL>>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*******************************************************************************/

#pragma once

#include "QtnProperty/Config.h"

#include <QMetaObject>

#include <vector>

class QTN_IMPORT_EXPORT QtnConnections
	: public std::vector<QMetaObject::Connection>
{
	Q_DISABLE_COPY(QtnConnections)

public:
	void disconnect();

	QtnConnections();
	~QtnConnections();
};
