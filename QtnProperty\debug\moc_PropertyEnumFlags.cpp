/****************************************************************************
** Meta object code from reading C++ file 'PropertyEnumFlags.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../Core/PropertyEnumFlags.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'PropertyEnumFlags.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtnPropertyEnumFlagsBase_t {
    QByteArrayData data[1];
    char stringdata0[25];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyEnumFlagsBase_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyEnumFlagsBase_t qt_meta_stringdata_QtnPropertyEnumFlagsBase = {
    {
QT_MOC_LITERAL(0, 0, 24) // "QtnPropertyEnumFlagsBase"

    },
    "QtnPropertyEnumFlagsBase"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyEnumFlagsBase[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QtnPropertyEnumFlagsBase::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject QtnPropertyEnumFlagsBase::staticMetaObject = {
    { &QtnSinglePropertyBase<QtnEnumFlagsValueType>::staticMetaObject, qt_meta_stringdata_QtnPropertyEnumFlagsBase.data,
      qt_meta_data_QtnPropertyEnumFlagsBase,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyEnumFlagsBase::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyEnumFlagsBase::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyEnumFlagsBase.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyBase<QtnEnumFlagsValueType>::qt_metacast(_clname);
}

int QtnPropertyEnumFlagsBase::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyBase<QtnEnumFlagsValueType>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyEnumFlagsCallback_t {
    QByteArrayData data[3];
    char stringdata0[37];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyEnumFlagsCallback_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyEnumFlagsCallback_t qt_meta_stringdata_QtnPropertyEnumFlagsCallback = {
    {
QT_MOC_LITERAL(0, 0, 28), // "QtnPropertyEnumFlagsCallback"
QT_MOC_LITERAL(1, 29, 0), // ""
QT_MOC_LITERAL(2, 30, 6) // "parent"

    },
    "QtnPropertyEnumFlagsCallback\0\0parent"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyEnumFlagsCallback[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       2,   18, // constructors
       0,       // flags
       0,       // signalCount

 // constructors: parameters
    0x80000000 | 1, QMetaType::QObjectStar,    2,
    0x80000000 | 1,

 // constructors: name, argc, parameters, tag, flags
       0,    1,   14,    1, 0x0e /* Public */,
       0,    0,   17,    1, 0x2e /* Public | MethodCloned */,

       0        // eod
};

void QtnPropertyEnumFlagsCallback::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::CreateInstance) {
        switch (_id) {
        case 0: { QtnPropertyEnumFlagsCallback *_r = new QtnPropertyEnumFlagsCallback((*reinterpret_cast< QObject*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        case 1: { QtnPropertyEnumFlagsCallback *_r = new QtnPropertyEnumFlagsCallback();
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        default: break;
        }
    }
    Q_UNUSED(_o);
}

const QMetaObject QtnPropertyEnumFlagsCallback::staticMetaObject = {
    { &QtnSinglePropertyCallback<QtnPropertyEnumFlagsBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyEnumFlagsCallback.data,
      qt_meta_data_QtnPropertyEnumFlagsCallback,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyEnumFlagsCallback::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyEnumFlagsCallback::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyEnumFlagsCallback.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyCallback<QtnPropertyEnumFlagsBase>::qt_metacast(_clname);
}

int QtnPropertyEnumFlagsCallback::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyCallback<QtnPropertyEnumFlagsBase>::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QtnPropertyEnumFlags_t {
    QByteArrayData data[3];
    char stringdata0[29];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtnPropertyEnumFlags_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtnPropertyEnumFlags_t qt_meta_stringdata_QtnPropertyEnumFlags = {
    {
QT_MOC_LITERAL(0, 0, 20), // "QtnPropertyEnumFlags"
QT_MOC_LITERAL(1, 21, 0), // ""
QT_MOC_LITERAL(2, 22, 6) // "parent"

    },
    "QtnPropertyEnumFlags\0\0parent"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtnPropertyEnumFlags[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       2,   18, // constructors
       0,       // flags
       0,       // signalCount

 // constructors: parameters
    0x80000000 | 1, QMetaType::QObjectStar,    2,
    0x80000000 | 1,

 // constructors: name, argc, parameters, tag, flags
       0,    1,   14,    1, 0x0e /* Public */,
       0,    0,   17,    1, 0x2e /* Public | MethodCloned */,

       0        // eod
};

void QtnPropertyEnumFlags::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::CreateInstance) {
        switch (_id) {
        case 0: { QtnPropertyEnumFlags *_r = new QtnPropertyEnumFlags((*reinterpret_cast< QObject*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        case 1: { QtnPropertyEnumFlags *_r = new QtnPropertyEnumFlags();
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        default: break;
        }
    }
    Q_UNUSED(_o);
}

const QMetaObject QtnPropertyEnumFlags::staticMetaObject = {
    { &QtnSinglePropertyValue<QtnPropertyEnumFlagsBase>::staticMetaObject, qt_meta_stringdata_QtnPropertyEnumFlags.data,
      qt_meta_data_QtnPropertyEnumFlags,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QtnPropertyEnumFlags::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtnPropertyEnumFlags::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtnPropertyEnumFlags.stringdata0))
        return static_cast<void*>(this);
    return QtnSinglePropertyValue<QtnPropertyEnumFlagsBase>::qt_metacast(_clname);
}

int QtnPropertyEnumFlags::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnSinglePropertyValue<QtnPropertyEnumFlagsBase>::qt_metacall(_c, _id, _a);
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
