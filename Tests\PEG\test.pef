#include "QtnProperty/Enum.h"
#include_cpp <vector>
#include_h "QtnProperty/PropertyCore.h"
#include_h "QtnProperty/PropertyGUI.h"

property_set Test1
{
    id = 1;
    description = "Test property_set description";
    state =0 ;
    Int a
    {
        id = 2;
        description = "Descripion";
        value = 5;
        stepValue = -1;
        maxValue = 10;
    }

QString text{
    id = 3;
    value=QString("#^{};");
    description = "defrf\"sde\"""deerf3rf"
    "derf r g\r\nreg r{}""dfrgerg"
    "fwrewre";
}

}

property_set Test2{
    id = 4;
}


code_h
{
    // declaration
    void aaa();
}

property_set Test3 {
    id = 5;
    property_set YY yy
    {
        id = 6;
        description = QString("ss")+QString("ss");
        // rectangle property
        QRect rect {value = QRect(10, 10, 10, 10);}

        QString s;
    }

    property_set SS iis
    {
        id = 7;
        Bool a{ value = true; id = 8; }

        property_set AA aa
        {
            id = 9;
           code_h
           {
               private:
                   bool m_s;
           }
           code_cpp
           {
               // AA cpp code
           }
        }
    }

    Bool u
    {
        id = 10;
        value = true;
        slot propertyDidChange
        {
            // sub u propertyDidChange
        }
    }

    slot propertyDidChange
    {
        // this propertyDidChange
    }

    slot u.propertyWillChange
    {
        // u.propertyWillChange code
    }
    
    /*
     * external property set
     **/
    extern property_set Test2 xx {}

    extern property_set Test2 tt;

    extern property_set SS s
    {
        a.value = false;

        slot a.propertyWillChange
        {
            yy.rect = QRect(1, 1, 1, 1);
            // ss propertyWillChange
        }

        slot a.propertyValueAccept
        {
            // s.a.propertyValueAccept
        }

    }

    Bool ww {id = 11; }

    BoolCallback bc
    {
        id = 12;
        callbackValueGet = [this]()->bool {
                return ww;
        };

        callbackValueAccepted = [](bool value)->bool {
            if (value) {
                return true;
            } else {
                return false;
            }
        };

        callbackValueEqual = [](bool)->bool { return false; };

        callbackValueSet = [this](bool value, QtnPropertyChangeReason /*reason*/) {
            m_s = value;
        };
    }

    code_h
    {
        public:
            bool m_s;
    }

}

    code_cpp{
        void aaa()
        {

        }
    }

enum LANGUAGE
{
    ENG(3, "English")
}

enum TYPE{}

enum COLOR {
    RED (10, "Red"),
    BLUE (22, "Blue") hidden obsolete,
    YELLOW (1, "Yellow")
}

enum MASK {
    ONE (1, "One"),
    TWO (2, "Two"),
    FOUR (4, "Four")
}

property_set AllPropertyTypes
{
    id = 13;
    Bool bp { id = 14; }
    BoolCallback bpc
    {
        callbackValueGet = [this]() { return _b; };
        callbackValueSet = [this](bool v, QtnPropertyChangeReason /*reason*/) { _b = v; };
        id = 15;
    }

    Int ip { id = 16; }
    IntCallback ipc
    {
        callbackValueGet = [this]() { return _i; };
        callbackValueSet = [this](qint32 v, QtnPropertyChangeReason /*reason*/) { _i =v; };
        id = 17;
    }

    UInt up { id = 18; }
    UIntCallback upc
    {
        callbackValueGet = [this]() { return _ui; };
        callbackValueSet = [this](quint32 v, QtnPropertyChangeReason /*reason*/) { _ui = v; };
        id = 19;
    }

    Float fp { id = 20; }
    FloatCallback fpc
    {
        callbackValueGet = [this]() { return _f; };
        callbackValueSet = [this](float v, QtnPropertyChangeReason /*reason*/) { _f = v; };
        id = 21;
    }

    Double dp { id = 22; }
    DoubleCallback dpc
    {
        callbackValueGet = [this]() { return _d; };
        callbackValueSet = [this](double v, QtnPropertyChangeReason /*reason*/) { _d = v; };
        id = 23;
    }

    QString sp { id = 24; }
    QStringCallback spc
    {
        callbackValueGet = [this]() { return _s; };
        callbackValueSet = [this](QString v, QtnPropertyChangeReason /*reason*/) { _s = v; };
        id = 25;
    }

    QRect rp { id = 26; }
    QRectCallback rpc
    {
        callbackValueGet = [this]() { return _r; };
        callbackValueSet = [this](QRect v, QtnPropertyChangeReason /*reason*/) { _r = v; };
        id = 27;
    }

    QPoint pp { id = 28; }
    QPointCallback ppc
    {
        callbackValueGet = [this]() { return _p; };
        callbackValueSet = [this](QPoint v, QtnPropertyChangeReason /*reason*/) { _p = v; };
        id = 29;
    }

    QSize szp { id = 30; }
    QSizeCallback szpc
    {
        callbackValueGet = [this]() { return _sz; };
        callbackValueSet = [this](QSize v, QtnPropertyChangeReason /*reason*/) { _sz = v; };
        id = 31;
    }

    Enum ep
    {
        enumInfo = &COLOR::info();
        value = COLOR::BLUE;
        id = 32;
    }
    EnumCallback epc
    {
        enumInfo = &COLOR::info();
        callbackValueGet = [this]() { return _e; };
        callbackValueSet = [this](QtnEnumValueType v, QtnPropertyChangeReason /*reason*/) { _e = v; };
        id = 33;
    }

    EnumFlags efp
    {
        enumInfo = &MASK::info();
        value = MASK::ONE|MASK::FOUR;
        id = 34;
    }
    EnumFlagsCallback efpc
    {
        enumInfo = &MASK::info();
        callbackValueGet = [this]() { return _ef; };
        callbackValueSet = [this](QtnEnumFlagsValueType v, QtnPropertyChangeReason /*reason*/) { _ef = v; };
        id = 35;
    }

    QColor cp
    {
        value = QColor(Qt::blue);
        id = 36;
    }
    QColorCallback cpc
    {
        callbackValueGet = [this]() { return _cl; };
        callbackValueSet = [this](QColor v, QtnPropertyChangeReason /*reason*/) { _cl = v; };
        id = 37;
    }

    QFont fnp
    {
        value = QFont("Courier", 10);
        id = 38;
    }
    QFontCallback fnpc
    {
        callbackValueGet = [this]() { return _fn; };
        callbackValueSet = [this](QFont v, QtnPropertyChangeReason /*reason*/) { _fn = v; };
        id = 39;
    }
    
    Button bttn
    {
        id = 40;
    }

    QPointF ppf { id = 41; }
    QPointFCallback ppfc
    {
        callbackValueGet = [this]() { return _pf; };
        callbackValueSet = [this](QPointF v, QtnPropertyChangeReason /*reason*/) { _pf = v; };
        id = 42;
    }

    QRectF rpf { id = 43; }
    QRectFCallback rpfc
    {
        callbackValueGet = [this]() { return _rf; };
        callbackValueSet = [this](QRectF v, QtnPropertyChangeReason /*reason*/) { _rf = v; };
        id = 44;
    }
    
    QSizeF szpf { id = 45; }
    QSizeFCallback szpfc
    {
        callbackValueGet = [this]() { return _szf; };
        callbackValueSet = [this](QSizeF v, QtnPropertyChangeReason /*reason*/) { _szf = v; };
        id = 46;
    }

    AllPropertyTypes()
        : _b(true)
    {
        _i =12;
        _ui = 9;
        _f = 0.2f;
        _d = 32.4;
        _s = "name";
        _r = QRect(10, 10, 10, 10);
        _rf = QRectF(10.1, 10.2, 10.3, 10.4);
        _p = QPoint(9, 2);
        _pf = QPointF(9.9, 2.2);
        _sz = QSize(33, 21);
        _szf = QSizeF(33.0, 21.9);
        _e = COLOR::RED;
        _ef = MASK::ONE|MASK::FOUR;
        _cl = QColor(Qt::red);
        _fn = QFont("Arial", 19);
    }

    ~AllPropertyTypes()
    {
        _b = false;
    }

    code_h
    {
    private:
        bool _b;
        qint32 _i;
        quint32 _ui;
        float _f;
        double _d;
        QString _s;
        QRect _r;
        QRectF _rf;
        QPoint _p;
        QPointF _pf;
        QSize _sz;
        QSizeF _szf;
        QtnEnumValueType _e;
        QtnEnumFlagsValueType _ef;
        QColor _cl;
        QFont _fn;
    }
}

/*
 *    NONE(-1, "None") hidden,
    ENG(0, "English"),
    FR(1, "French"),
    GM(2, "German")

enum MY_TYPE
{
    MY_TYPE1(1, "My type 1"),
    MY_TYPE2(2, "My type 2")
}
*/

enum MY_TYPE
{
    MY_TYPE1(1, "My type 1"),
    MY_TYPE2(2, "My type 2")
}

property_set Test12
{
    Enum p
    {
        value = MY_TYPE::MY_TYPE1;
        enumInfo = &MY_TYPE::info();
    }
}