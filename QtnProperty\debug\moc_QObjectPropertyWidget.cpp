/****************************************************************************
** Meta object code from reading C++ file 'QObjectPropertyWidget.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../QObjectPropertyWidget.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'QObjectPropertyWidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QObjectPropertyWidget_t {
    QByteArrayData data[11];
    char stringdata0[130];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QObjectPropertyWidget_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QObjectPropertyWidget_t qt_meta_stringdata_QObjectPropertyWidget = {
    {
QT_MOC_LITERAL(0, 0, 21), // "QObjectPropertyWidget"
QT_MOC_LITERAL(1, 22, 18), // "deselectAllObjects"
QT_MOC_LITERAL(2, 41, 0), // ""
QT_MOC_LITERAL(3, 42, 12), // "selectObject"
QT_MOC_LITERAL(4, 55, 6), // "object"
QT_MOC_LITERAL(5, 62, 12), // "addSelection"
QT_MOC_LITERAL(6, 75, 13), // "selectObjects"
QT_MOC_LITERAL(7, 89, 7), // "Objects"
QT_MOC_LITERAL(8, 97, 7), // "objects"
QT_MOC_LITERAL(9, 105, 14), // "deselectObject"
QT_MOC_LITERAL(10, 120, 9) // "destroyed"

    },
    "QObjectPropertyWidget\0deselectAllObjects\0"
    "\0selectObject\0object\0addSelection\0"
    "selectObjects\0Objects\0objects\0"
    "deselectObject\0destroyed"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QObjectPropertyWidget[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       7,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   49,    2, 0x0a /* Public */,
       3,    2,   50,    2, 0x0a /* Public */,
       3,    1,   55,    2, 0x2a /* Public | MethodCloned */,
       6,    2,   58,    2, 0x0a /* Public */,
       6,    1,   63,    2, 0x2a /* Public | MethodCloned */,
       9,    2,   66,    2, 0x0a /* Public */,
       9,    1,   71,    2, 0x2a /* Public | MethodCloned */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void, QMetaType::QObjectStar, QMetaType::Bool,    4,    5,
    QMetaType::Void, QMetaType::QObjectStar,    4,
    QMetaType::Void, 0x80000000 | 7, QMetaType::Bool,    8,    5,
    QMetaType::Void, 0x80000000 | 7,    8,
    QMetaType::Void, QMetaType::QObjectStar, QMetaType::Bool,    4,   10,
    QMetaType::Void, QMetaType::QObjectStar,    4,

       0        // eod
};

void QObjectPropertyWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        QObjectPropertyWidget *_t = static_cast<QObjectPropertyWidget *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->deselectAllObjects(); break;
        case 1: _t->selectObject((*reinterpret_cast< QObject*(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2]))); break;
        case 2: _t->selectObject((*reinterpret_cast< QObject*(*)>(_a[1]))); break;
        case 3: _t->selectObjects((*reinterpret_cast< const Objects(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2]))); break;
        case 4: _t->selectObjects((*reinterpret_cast< const Objects(*)>(_a[1]))); break;
        case 5: _t->deselectObject((*reinterpret_cast< QObject*(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2]))); break;
        case 6: _t->deselectObject((*reinterpret_cast< QObject*(*)>(_a[1]))); break;
        default: ;
        }
    }
}

const QMetaObject QObjectPropertyWidget::staticMetaObject = {
    { &QtnPropertyWidgetEx::staticMetaObject, qt_meta_stringdata_QObjectPropertyWidget.data,
      qt_meta_data_QObjectPropertyWidget,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QObjectPropertyWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QObjectPropertyWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QObjectPropertyWidget.stringdata0))
        return static_cast<void*>(this);
    return QtnPropertyWidgetEx::qt_metacast(_clname);
}

int QObjectPropertyWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QtnPropertyWidgetEx::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 7;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
