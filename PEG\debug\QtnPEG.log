﻿Build started 8/7/2025 4:08:04 PM.
Logging verbosity is set to: Normal.     1>Project "G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj" on node 2 (Build target(s)).
     1>InitializeBuildStatus:
         Touching "debug\QtnPEG.tlog\unsuccessfulbuild".
       CustomBuild:
         Generate moc_predefs.h
         Bison PropertyEnum.y
         'win_bison' is not recognized as an internal or external command,
         operable program or batch file.
         Flex PropertyEnum.l
         'win_flex' is not recognized as an internal or external command,
         operable program or batch file.
     1>C:\Program Files (x86)\MSBuild\Microsoft.Cpp\v4.0\V140\Microsoft.CppCommon.targets(171,5): error MSB6006: "cmd.exe" exited with code 9009.
     1>Done Building Project "G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj" (Build target(s)) -- FAILED.

Build FAILED.

       "G:\Code\Qt\QtnProperty\PEG\QtnPEG.vcxproj" (Build target) (1) ->
       (CustomBuild target) -> 
         C:\Program Files (x86)\MSBuild\Microsoft.Cpp\v4.0\V140\Microsoft.CppCommon.targets(171,5): error MSB6006: "cmd.exe" exited with code 9009.

    0 Warning(s)
    1 Error(s)

Time Elapsed 00:00:00.21
